"""
Video sharing routes for managing video permissions and access control.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from models.database import get_db, User, VideoShare
from models.schemas import (
    VideoShareCreate, VideoShareUpdate, VideoShareResponse, PermissionLevel,
    BulkShareRequest, BulkShareResponse, SharingAnalyticsRequest, SharingAnalyticsResponse
)
from middleware.auth import get_current_user
from services.sharing_service import SharingService

router = APIRouter(tags=["sharing"])

@router.post("/videos/{video_id}/share", response_model=VideoShareResponse)
async def share_video(
    video_id: int,
    target_username: str,
    permission_level: PermissionLevel,
    expires_at: Optional[datetime] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Share a video with another user."""
    # Find target user by username
    target_user = db.query(User).filter(User.username == target_username).first()
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Target user not found"
        )
    
    # Prevent sharing with self
    if target_user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot share video with yourself"
        )
    
    share = SharingService.share_video(
        db=db,
        video_id=video_id,
        target_user_id=target_user.id,
        permission_level=permission_level,
        shared_by_user_id=current_user.id,
        expires_at=expires_at
    )
    
    # Add username information for response
    share.sharer_username = current_user.username
    share.user_username = target_user.username
    
    return share

@router.get("/videos/{video_id}/shares", response_model=List[VideoShareResponse])
async def get_video_shares(
    video_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all shares for a video (owner only)."""
    shares = SharingService.get_video_shares(db, video_id, current_user.id)
    
    # Add username information
    for share in shares:
        sharer = db.query(User).filter(User.id == share.shared_by).first()
        target_user = db.query(User).filter(User.id == share.user_id).first()
        share.sharer_username = sharer.username if sharer else None
        share.user_username = target_user.username if target_user else None
    
    return shares

@router.get("/shared-with-me", response_model=List[VideoShareResponse])
async def get_shared_videos(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all videos shared with the current user."""
    shares = SharingService.get_user_shared_videos(db, current_user.id)
    
    # Add username information
    for share in shares:
        sharer = db.query(User).filter(User.id == share.shared_by).first()
        share.sharer_username = sharer.username if sharer else None
        share.user_username = current_user.username
    
    return shares

@router.put("/shares/{share_id}", response_model=VideoShareResponse)
async def update_video_share(
    share_id: int,
    share_update: VideoShareUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update an existing video share."""
    share = SharingService.update_video_share(db, share_id, share_update, current_user.id)
    
    # Add username information
    sharer = db.query(User).filter(User.id == share.shared_by).first()
    target_user = db.query(User).filter(User.id == share.user_id).first()
    share.sharer_username = sharer.username if sharer else None
    share.user_username = target_user.username if target_user else None
    
    return share

@router.delete("/shares/{share_id}")
async def revoke_video_share(
    share_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Revoke a video share."""
    success = SharingService.revoke_video_share(db, share_id, current_user.id)
    if success:
        return {"message": "Share revoked successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke share"
        )

@router.get("/videos/{video_id}/my-permission")
async def get_my_video_permission(
    video_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get the current user's permission level for a video."""
    permission = SharingService.get_user_permission(db, video_id, current_user.id)
    return {
        "video_id": video_id,
        "user_id": current_user.id,
        "permission_level": permission.value if permission else None,
        "has_access": permission is not None
    }

# New endpoints for enhanced sharing features

@router.get("/shared-by-me", response_model=List[VideoShareResponse])
async def get_shared_by_me(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search by video title or username"),
    sort_by: str = Query("created_at", description="Sort field"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get videos shared by the current user with pagination and search."""
    from sqlalchemy import or_, desc, asc

    # Base query for shares created by current user
    query = db.query(VideoShare).filter(VideoShare.shared_by == current_user.id)

    # Add search filter
    if search:
        query = query.join(Video).join(User, VideoShare.user_id == User.id).filter(
            or_(
                Video.title.ilike(f"%{search}%"),
                Video.original_filename.ilike(f"%{search}%"),
                User.username.ilike(f"%{search}%")
            )
        )

    # Add sorting
    if sort_by == "created_at":
        query = query.order_by(desc(VideoShare.shared_at))
    elif sort_by == "access_count":
        query = query.order_by(desc(VideoShare.access_count))
    elif sort_by == "last_accessed":
        query = query.order_by(desc(VideoShare.last_accessed_at))

    # Apply pagination
    offset = (page - 1) * limit
    shares = query.offset(offset).limit(limit).all()

    # Add username information
    for share in shares:
        sharer = db.query(User).filter(User.id == share.shared_by).first()
        target_user = db.query(User).filter(User.id == share.user_id).first()
        share.sharer_username = sharer.username if sharer else None
        share.user_username = target_user.username if target_user else None

    return shares

@router.get("/shared-with-me", response_model=List[VideoShareResponse])
async def get_shared_with_me_paginated(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    filter_by: Optional[str] = Query(None, description="Filter by permission level"),
    search: Optional[str] = Query(None, description="Search by video title or sharer name"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get videos shared with the current user with pagination and filtering."""
    from sqlalchemy import or_, desc

    # Base query for shares with current user
    query = db.query(VideoShare).filter(
        VideoShare.user_id == current_user.id,
        VideoShare.is_active == True
    )

    # Add permission filter
    if filter_by:
        try:
            permission_filter = PermissionLevel(filter_by)
            query = query.filter(VideoShare.permission_level == permission_filter)
        except ValueError:
            pass  # Invalid permission level, ignore filter

    # Add search filter
    if search:
        query = query.join(Video).join(User, VideoShare.shared_by == User.id).filter(
            or_(
                Video.title.ilike(f"%{search}%"),
                Video.original_filename.ilike(f"%{search}%"),
                User.username.ilike(f"%{search}%")
            )
        )

    # Order by most recent
    query = query.order_by(desc(VideoShare.shared_at))

    # Apply pagination
    offset = (page - 1) * limit
    shares = query.offset(offset).limit(limit).all()

    # Add username information
    for share in shares:
        sharer = db.query(User).filter(User.id == share.shared_by).first()
        share.sharer_username = sharer.username if sharer else None
        share.user_username = current_user.username

    return shares

@router.post("/bulk-share", response_model=BulkShareResponse)
async def bulk_share_videos(
    bulk_request: BulkShareRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Share multiple videos with multiple users."""
    result = SharingService.bulk_share_videos(
        db=db,
        video_ids=bulk_request.video_ids,
        user_ids=bulk_request.user_ids,
        permission_level=bulk_request.permission_level,
        shared_by_user_id=current_user.id,
        expires_at=bulk_request.expires_at
    )

    # Add username information to created shares
    for share in result["created_shares"]:
        sharer = db.query(User).filter(User.id == share.shared_by).first()
        target_user = db.query(User).filter(User.id == share.user_id).first()
        share.sharer_username = sharer.username if sharer else None
        share.user_username = target_user.username if target_user else None

    return BulkShareResponse(**result)

@router.get("/analytics", response_model=SharingAnalyticsResponse)
async def get_sharing_analytics(
    start_date: Optional[datetime] = Query(None, description="Start date for analytics"),
    end_date: Optional[datetime] = Query(None, description="End date for analytics"),
    video_id: Optional[int] = Query(None, description="Filter by specific video"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get sharing analytics for the current user."""
    analytics = SharingService.get_sharing_analytics(
        db=db,
        user_id=current_user.id,
        start_date=start_date,
        end_date=end_date,
        video_id=video_id
    )

    return SharingAnalyticsResponse(**analytics)

@router.post("/shares/{share_id}/track-access")
async def track_video_access(
    share_id: int,
    request: Request,
    access_type: str = Query(..., description="Type of access: view, download, comment"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Track access to a shared video."""
    # Get client IP and user agent
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("user-agent")

    success = SharingService.track_video_access(
        db=db,
        share_id=share_id,
        user_id=current_user.id,
        access_type=access_type,
        ip_address=ip_address,
        user_agent=user_agent
    )

    if success:
        return {"message": "Access tracked successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to track access"
        )
