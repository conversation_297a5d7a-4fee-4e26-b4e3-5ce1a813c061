"""
Video sharing routes for managing video permissions and access control.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from models.database import get_db, User, VideoShare
from models.schemas import (
    VideoShareCreate, VideoShareUpdate, VideoShareResponse, PermissionLevel
)
from middleware.auth import get_current_user
from services.sharing_service import SharingService

router = APIRouter(tags=["sharing"])

@router.post("/videos/{video_id}/share", response_model=VideoShareResponse)
async def share_video(
    video_id: int,
    target_username: str,
    permission_level: PermissionLevel,
    expires_at: Optional[datetime] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Share a video with another user."""
    # Find target user by username
    target_user = db.query(User).filter(User.username == target_username).first()
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Target user not found"
        )
    
    # Prevent sharing with self
    if target_user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot share video with yourself"
        )
    
    share = SharingService.share_video(
        db=db,
        video_id=video_id,
        target_user_id=target_user.id,
        permission_level=permission_level,
        shared_by_user_id=current_user.id,
        expires_at=expires_at
    )
    
    # Add username information for response
    share.sharer_username = current_user.username
    share.user_username = target_user.username
    
    return share

@router.get("/videos/{video_id}/shares", response_model=List[VideoShareResponse])
async def get_video_shares(
    video_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all shares for a video (owner only)."""
    shares = SharingService.get_video_shares(db, video_id, current_user.id)
    
    # Add username information
    for share in shares:
        sharer = db.query(User).filter(User.id == share.shared_by).first()
        target_user = db.query(User).filter(User.id == share.user_id).first()
        share.sharer_username = sharer.username if sharer else None
        share.user_username = target_user.username if target_user else None
    
    return shares

@router.get("/shared-with-me", response_model=List[VideoShareResponse])
async def get_shared_videos(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all videos shared with the current user."""
    shares = SharingService.get_user_shared_videos(db, current_user.id)
    
    # Add username information
    for share in shares:
        sharer = db.query(User).filter(User.id == share.shared_by).first()
        share.sharer_username = sharer.username if sharer else None
        share.user_username = current_user.username
    
    return shares

@router.put("/shares/{share_id}", response_model=VideoShareResponse)
async def update_video_share(
    share_id: int,
    share_update: VideoShareUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update an existing video share."""
    share = SharingService.update_video_share(db, share_id, share_update, current_user.id)
    
    # Add username information
    sharer = db.query(User).filter(User.id == share.shared_by).first()
    target_user = db.query(User).filter(User.id == share.user_id).first()
    share.sharer_username = sharer.username if sharer else None
    share.user_username = target_user.username if target_user else None
    
    return share

@router.delete("/shares/{share_id}")
async def revoke_video_share(
    share_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Revoke a video share."""
    success = SharingService.revoke_video_share(db, share_id, current_user.id)
    if success:
        return {"message": "Share revoked successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke share"
        )

@router.get("/videos/{video_id}/my-permission")
async def get_my_video_permission(
    video_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get the current user's permission level for a video."""
    permission = SharingService.get_user_permission(db, video_id, current_user.id)
    return {
        "video_id": video_id,
        "user_id": current_user.id,
        "permission_level": permission.value if permission else None,
        "has_access": permission is not None
    }
