#!/usr/bin/env python3
"""
Migration script to add sharing analytics to TagTok database.
This script will:
1. Create a backup of the existing database
2. Add analytics fields to video_shares table
3. Create new sharing analytics tables
4. Preserve all existing sharing data
"""

import os
import sys
import shutil
import sqlite3
from datetime import datetime
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from models.database import create_tables, SessionLocal
from sqlalchemy.orm import Session
from sqlalchemy import text

def backup_database(db_path: str) -> str:
    """Create a backup of the existing database"""
    if not os.path.exists(db_path):
        print(f"Database file {db_path} does not exist. Creating new database.")
        return None
    
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(db_path, backup_path)
    print(f"Database backed up to: {backup_path}")
    return backup_path

def add_analytics_columns_to_video_shares(db: Session):
    """Add analytics columns to video_shares table if they don't exist"""
    try:
        # Check if access_count column exists
        db.execute(text("SELECT access_count FROM video_shares LIMIT 1"))
        print("Analytics columns already exist in video_shares table")
        return True
    except Exception:
        # Columns don't exist, add them
        try:
            db.execute(text("ALTER TABLE video_shares ADD COLUMN access_count INTEGER DEFAULT 0"))
            db.execute(text("ALTER TABLE video_shares ADD COLUMN last_accessed_at TIMESTAMP"))
            db.commit()
            print("Added analytics columns to video_shares table")
            return True
        except Exception as e:
            print(f"Failed to add analytics columns: {e}")
            return False

def create_analytics_tables(db: Session):
    """Create new analytics tables"""
    try:
        # Check if sharing_activities table exists
        db.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='sharing_activities'"))
        result = db.fetchone()
        if result:
            print("Analytics tables already exist")
            return True
    except Exception:
        pass
    
    try:
        # Create sharing_activities table
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS sharing_activities (
                id INTEGER PRIMARY KEY,
                video_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                action_type VARCHAR NOT NULL,
                target_user_id INTEGER,
                permission_level VARCHAR,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata JSON,
                FOREIGN KEY(video_id) REFERENCES videos(id),
                FOREIGN KEY(user_id) REFERENCES users(id),
                FOREIGN KEY(target_user_id) REFERENCES users(id)
            )
        """))
        
        # Create video_access_logs table
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS video_access_logs (
                id INTEGER PRIMARY KEY,
                share_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ip_address VARCHAR,
                user_agent VARCHAR,
                access_type VARCHAR NOT NULL,
                FOREIGN KEY(share_id) REFERENCES video_shares(id),
                FOREIGN KEY(user_id) REFERENCES users(id)
            )
        """))
        
        # Create indexes for performance
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_sharing_activities_video_id ON sharing_activities(video_id)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_sharing_activities_user_id ON sharing_activities(user_id)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_sharing_activities_created_at ON sharing_activities(created_at)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_video_access_logs_share_id ON video_access_logs(share_id)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_video_access_logs_user_id ON video_access_logs(user_id)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_video_access_logs_accessed_at ON video_access_logs(accessed_at)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_video_shares_access_count ON video_shares(access_count)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_video_shares_last_accessed_at ON video_shares(last_accessed_at)"))
        
        db.commit()
        print("Created analytics tables and indexes")
        return True
    except Exception as e:
        print(f"Failed to create analytics tables: {e}")
        return False

def run_migration():
    """Run the complete migration"""
    print("Starting TagTok sharing analytics migration...")
    
    # Get database path from environment or use default
    db_path = os.getenv("DATABASE_URL", "sqlite:///db/tagTok.db")
    if db_path.startswith("sqlite:///"):
        db_file_path = db_path.replace("sqlite:///", "")
    else:
        print(f"Unsupported database URL: {db_path}")
        return False
    
    # Create backup
    backup_path = backup_database(db_file_path)
    
    try:
        # Create database session
        db = SessionLocal()

        try:
            # Add analytics columns to existing video_shares table
            if not add_analytics_columns_to_video_shares(db):
                raise Exception("Failed to add analytics columns to video_shares table")

            # Create new analytics tables
            if not create_analytics_tables(db):
                raise Exception("Failed to create analytics tables")
            
            print("Migration completed successfully!")
            print("\nNew features available:")
            print("1. Sharing access tracking")
            print("2. Detailed sharing analytics")
            print("3. Bulk sharing operations")
            print("4. Enhanced sharing management")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"Migration failed: {e}")
        if backup_path and os.path.exists(backup_path):
            print(f"Restoring from backup: {backup_path}")
            shutil.copy2(backup_path, db_file_path)
        return False

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)
