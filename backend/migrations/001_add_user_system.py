#!/usr/bin/env python3
"""
Migration script to add user system to existing TagTok database.
This script will:
1. Create a backup of the existing database
2. Add new user management tables
3. Create a default admin user
4. Preserve all existing video data
"""

import os
import sys
import shutil
import sqlite3
from datetime import datetime
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from models.database import create_tables, SessionLocal, User, Video
from sqlalchemy.orm import Session
from sqlalchemy import text
from passlib.context import CryptContext

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def backup_database(db_path: str) -> str:
    """Create a backup of the existing database"""
    if not os.path.exists(db_path):
        print(f"Database file {db_path} does not exist. Creating new database.")
        return None
    
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(db_path, backup_path)
    print(f"Database backed up to: {backup_path}")
    return backup_path

def create_default_admin_user(db: Session) -> User:
    """Create a default admin user"""
    # Check if any users exist
    existing_user = db.query(User).first()
    if existing_user:
        print("Users already exist in the database. Skipping admin user creation.")
        return existing_user
    
    # Create default admin user
    hashed_password = pwd_context.hash("admin123")  # Change this in production!
    admin_user = User(
        username="admin",
        email="<EMAIL>",
        hashed_password=hashed_password,
        full_name="TagTok Administrator",
        is_active=True,
        is_superuser=True
    )
    
    db.add(admin_user)
    db.commit()
    db.refresh(admin_user)
    
    print(f"Created default admin user: {admin_user.username}")
    print("Default password: admin123 (CHANGE THIS IN PRODUCTION!)")
    return admin_user

def add_user_id_column(db: Session):
    """Add user_id column to videos table if it doesn't exist"""
    try:
        # Check if column exists
        db.execute(text("SELECT user_id FROM videos LIMIT 1"))
        print("user_id column already exists in videos table")
        return True
    except Exception:
        # Column doesn't exist, add it
        try:
            db.execute(text("ALTER TABLE videos ADD COLUMN user_id INTEGER"))
            db.commit()
            print("Added user_id column to videos table")
            return True
        except Exception as e:
            print(f"Failed to add user_id column: {e}")
            return False

def assign_videos_to_admin(db: Session, admin_user: User):
    """Assign all existing videos to the admin user"""
    try:
        # Use raw SQL to update videos
        result = db.execute(text("UPDATE videos SET user_id = :user_id WHERE user_id IS NULL"), {"user_id": admin_user.id})
        db.commit()
        print(f"Assigned {result.rowcount} videos to admin user")
    except Exception as e:
        print(f"Error assigning videos to admin: {e}")
        return False
    return True

def run_migration():
    """Run the complete migration"""
    print("Starting TagTok user system migration...")
    
    # Get database path from environment or use default
    db_path = os.getenv("DATABASE_URL", "sqlite:///db/tagTok.db")
    if db_path.startswith("sqlite:///"):
        db_file_path = db_path.replace("sqlite:///", "")
    else:
        print(f"Unsupported database URL: {db_path}")
        return False
    
    # Create backup
    backup_path = backup_database(db_file_path)
    
    try:
        # Create database session first to check existing structure
        db = SessionLocal()

        try:
            # Create new tables first (this will create users, video_shares, user_sessions)
            print("Creating new database tables...")
            create_tables()

            # Add user_id column to existing videos table
            if not add_user_id_column(db):
                raise Exception("Failed to add user_id column to videos table")

            # Create default admin user
            admin_user = create_default_admin_user(db)

            # Assign existing videos to admin
            if not assign_videos_to_admin(db, admin_user):
                raise Exception("Failed to assign videos to admin user")
            
            print("Migration completed successfully!")
            print("\nNext steps:")
            print("1. Change the default admin password")
            print("2. Create additional user accounts as needed")
            print("3. Test the authentication system")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"Migration failed: {e}")
        if backup_path and os.path.exists(backup_path):
            print(f"Restoring from backup: {backup_path}")
            shutil.copy2(backup_path, db_file_path)
        return False

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)
