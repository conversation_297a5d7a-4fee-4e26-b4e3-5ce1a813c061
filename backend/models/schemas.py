from pydantic import BaseModel, Field, EmailStr
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

# Permission levels
class PermissionLevel(str, Enum):
    VIEW = "view"
    DOWNLOAD = "download"
    COMMENT = "comment"
    OWNER = "owner"

# Video schemas
class VideoBase(BaseModel):
    title: Optional[str] = None
    original_filename: str

class VideoCreate(VideoBase):
    pass

class VideoUpdate(BaseModel):
    title: Optional[str] = None

class VideoResponse(VideoBase):
    id: int
    filename: str
    file_path: str
    file_size: int
    duration: Optional[float] = None
    width: Optional[int] = None
    height: Optional[int] = None
    fps: Optional[float] = None
    thumbnail_path: Optional[str] = None
    transcript: Optional[str] = None
    transcript_language: Optional[str] = None
    upload_date: datetime
    processed: bool
    processing_status: str
    processing_progress: int = 0
    source_url: Optional[str] = None
    download_status: Optional[str] = None
    download_error: Optional[str] = None
    download_progress: int = 0
    user_id: Optional[int] = None
    owner_username: Optional[str] = None
    user_permission: Optional[PermissionLevel] = None
    tags: List["TagResponse"] = []
    recipe: Optional["RecipeResponse"] = None

    class Config:
        from_attributes = True

# Tag schemas
class TagBase(BaseModel):
    name: str
    color: str = Field(..., pattern=r'^#[0-9A-Fa-f]{6}$')  # Hex color validation
    description: Optional[str] = None

class TagCreate(TagBase):
    pass

class TagUpdate(BaseModel):
    name: Optional[str] = None
    color: Optional[str] = Field(None, pattern=r'^#[0-9A-Fa-f]{6}$')
    description: Optional[str] = None

class TagResponse(TagBase):
    id: int
    created_date: datetime
    usage_count: int
    
    class Config:
        from_attributes = True

# Processing job schemas
class ProcessingJobResponse(BaseModel):
    id: int
    video_id: int
    job_type: str
    status: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    class Config:
        from_attributes = True

# Search and filter schemas
class VideoFilter(BaseModel):
    tags: Optional[List[str]] = None
    search: Optional[str] = None
    language: Optional[str] = None
    processed: Optional[bool] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    category: Optional[str] = None  # Filter by tag category (Business, Tech, Design, etc.)

class PaginationParams(BaseModel):
    skip: int = Field(0, ge=0)
    limit: int = Field(50, ge=1, le=100)

# Analytics schemas
class AnalyticsResponse(BaseModel):
    total_videos: int
    total_tags: int
    total_duration: float
    processed_videos: int
    pending_videos: int
    top_tags: List[Dict[str, Any]]
    language_distribution: Dict[str, int]
    upload_timeline: List[Dict[str, Any]]
    duration_distribution: Dict[str, int]

# Export schemas
class ExportFormat(BaseModel):
    format: str = Field(..., pattern=r'^(csv|json)$')
    include_transcript: bool = True
    include_tags: bool = True

# Upload response
class UploadResponse(BaseModel):
    message: str
    uploaded_files: List[str]
    failed_files: List[Dict[str, str]]
    total_uploaded: int

# Download request
class DownloadRequest(BaseModel):
    url: str = Field(..., description="URL to download video from")
    quality: Optional[str] = Field(default="best", description="Video quality preference")
    format: Optional[str] = Field(default="mp4", description="Preferred video format")

# Download response
class DownloadResponse(BaseModel):
    message: str
    video_id: int
    download_status: str
    url: str

# Error response
class ErrorResponse(BaseModel):
    detail: str
    error_code: Optional[str] = None

# Health check response
class HealthResponse(BaseModel):
    status: str
    timestamp: datetime
    version: str = "1.0.0"

# Bulk operations schemas
class BulkTagOperation(BaseModel):
    video_ids: List[int] = Field(..., min_items=1, description="List of video IDs to operate on")
    tag_ids: List[int] = Field(..., min_items=1, description="List of tag IDs to add or remove")

class BulkDeleteRequest(BaseModel):
    video_ids: List[int] = Field(..., min_items=1, description="List of video IDs to delete")

class BulkOperationResponse(BaseModel):
    success: bool
    message: str
    processed_count: int
    failed_count: int
    failed_items: List[Dict[str, Any]] = []

class BulkTagOperationResponse(BulkOperationResponse):
    updated_videos: List[int] = []

class BulkDeleteResponse(BulkOperationResponse):
    deleted_videos: List[int] = []

# Recipe schemas
class IngredientBase(BaseModel):
    name: str
    amount: Optional[str] = None  # e.g., "2 cups", "1 tbsp"
    unit: Optional[str] = None    # e.g., "cups", "tbsp", "pieces"
    notes: Optional[str] = None   # e.g., "finely chopped", "room temperature"

class InstructionStep(BaseModel):
    step_number: int
    instruction: str
    time: Optional[str] = None    # e.g., "5 minutes", "until golden"
    temperature: Optional[str] = None  # e.g., "350°F", "medium heat"

class RecipeBase(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    prep_time: Optional[str] = None
    cook_time: Optional[str] = None
    total_time: Optional[str] = None
    servings: Optional[str] = None
    difficulty: Optional[str] = None
    cuisine_type: Optional[str] = None

class RecipeCreate(RecipeBase):
    video_id: int
    ingredients: Optional[List[IngredientBase]] = []
    instructions: Optional[List[InstructionStep]] = []
    extraction_confidence: Optional[float] = None

class RecipeUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    ingredients: Optional[List[IngredientBase]] = None
    instructions: Optional[List[InstructionStep]] = None
    prep_time: Optional[str] = None
    cook_time: Optional[str] = None
    total_time: Optional[str] = None
    servings: Optional[str] = None
    difficulty: Optional[str] = None
    cuisine_type: Optional[str] = None

class RecipeResponse(RecipeBase):
    id: int
    video_id: int
    ingredients: List[IngredientBase] = []
    instructions: List[InstructionStep] = []
    extracted_at: datetime
    extraction_confidence: Optional[float] = None

    class Config:
        from_attributes = True

class RecipeWithVideoResponse(RecipeBase):
    id: int
    video_id: int
    ingredients: List[IngredientBase] = []
    instructions: List[InstructionStep] = []
    extracted_at: datetime
    extraction_confidence: Optional[float] = None
    video_title: Optional[str] = None
    video_thumbnail_path: Optional[str] = None
    video_duration: Optional[float] = None

    class Config:
        from_attributes = True

# User schemas
class UserBase(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str = Field(..., min_length=6)

class UserUpdate(BaseModel):
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None

class UserResponse(UserBase):
    id: int
    is_active: bool
    is_superuser: bool
    created_at: datetime
    last_login: Optional[datetime] = None

    class Config:
        from_attributes = True

# Authentication schemas
class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    user: UserResponse

class TokenData(BaseModel):
    username: Optional[str] = None

class LoginRequest(BaseModel):
    username: str
    password: str

# Video sharing schemas
class VideoShareBase(BaseModel):
    video_id: int
    user_id: int
    permission_level: PermissionLevel

class VideoShareCreate(VideoShareBase):
    expires_at: Optional[datetime] = None

class VideoShareUpdate(BaseModel):
    permission_level: Optional[PermissionLevel] = None
    expires_at: Optional[datetime] = None
    is_active: Optional[bool] = None

class VideoShareResponse(VideoShareBase):
    id: int
    shared_at: datetime
    shared_by: int
    expires_at: Optional[datetime] = None
    is_active: bool
    access_count: int = 0
    last_accessed_at: Optional[datetime] = None
    sharer_username: Optional[str] = None
    user_username: Optional[str] = None

    class Config:
        from_attributes = True

# Sharing analytics schemas
class SharingActivityBase(BaseModel):
    video_id: int
    user_id: int
    action_type: str
    target_user_id: Optional[int] = None
    permission_level: Optional[PermissionLevel] = None

class SharingActivityCreate(SharingActivityBase):
    metadata: Optional[Dict[str, Any]] = None

class SharingActivityResponse(SharingActivityBase):
    id: int
    created_at: datetime
    metadata: Optional[Dict[str, Any]] = None
    user_username: Optional[str] = None
    target_user_username: Optional[str] = None
    video_title: Optional[str] = None

    class Config:
        from_attributes = True

class VideoAccessLogBase(BaseModel):
    share_id: int
    user_id: int
    access_type: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

class VideoAccessLogCreate(VideoAccessLogBase):
    pass

class VideoAccessLogResponse(VideoAccessLogBase):
    id: int
    accessed_at: datetime
    user_username: Optional[str] = None

    class Config:
        from_attributes = True

# Bulk sharing schemas
class BulkShareRequest(BaseModel):
    video_ids: List[int]
    user_ids: List[int]
    permission_level: PermissionLevel
    expires_at: Optional[datetime] = None

class BulkShareResponse(BaseModel):
    success_count: int
    failed_count: int
    errors: List[str] = []
    created_shares: List[VideoShareResponse] = []

# Sharing analytics schemas
class SharingAnalyticsRequest(BaseModel):
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    video_id: Optional[int] = None

class SharingAnalyticsResponse(BaseModel):
    total_shares: int
    total_accesses: int
    unique_users_shared_with: int
    most_shared_videos: List[Dict[str, Any]] = []
    most_accessed_shares: List[Dict[str, Any]] = []
    sharing_timeline: List[Dict[str, Any]] = []
    access_timeline: List[Dict[str, Any]] = []

# Update forward references
VideoResponse.model_rebuild()
