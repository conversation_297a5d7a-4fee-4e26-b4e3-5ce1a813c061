from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Float, Boolean, ForeignKey, Table, JSON, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
import os
import enum

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///db/tagTok.db")

# Create engine
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {}
)

# Create session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# Permission levels enum
class PermissionLevel(enum.Enum):
    VIEW = "view"
    DOWNLOAD = "download"
    COMMENT = "comment"
    OWNER = "owner"

# Association table for many-to-many relationship between videos and tags
video_tags = Table(
    'video_tags',
    Base.metadata,
    Column('video_id', Integer, <PERSON><PERSON>ey('videos.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True)
)

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    videos = relationship("Video", back_populates="owner")
    shared_videos = relationship("VideoShare", back_populates="user", foreign_keys="VideoShare.user_id")
    sessions = relationship("UserSession", back_populates="user")

class Video(Base):
    __tablename__ = "videos"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String, unique=True, index=True, nullable=False)
    original_filename = Column(String, nullable=False)
    title = Column(String, nullable=True)
    file_path = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)
    duration = Column(Float, nullable=True)
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    fps = Column(Float, nullable=True)
    thumbnail_path = Column(String, nullable=True)
    transcript = Column(Text, nullable=True)
    transcript_language = Column(String, nullable=True)
    upload_date = Column(DateTime(timezone=True), server_default=func.now())
    processed = Column(Boolean, default=False)
    processing_status = Column(String, default="pending")  # pending, processing, completed, failed
    processing_progress = Column(Integer, default=0)  # 0-100 percentage

    # Download-related fields
    source_url = Column(String, nullable=True)  # URL if downloaded from web
    download_status = Column(String, nullable=True)  # downloading, completed, failed
    download_error = Column(Text, nullable=True)  # Error message if download failed
    download_progress = Column(Integer, default=0)  # 0-100 percentage for downloads

    # User ownership
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Nullable for migration compatibility

    # Relationships
    owner = relationship("User", back_populates="videos")
    tags = relationship("Tag", secondary=video_tags, back_populates="videos")
    recipe = relationship("Recipe", back_populates="video", uselist=False)
    shares = relationship("VideoShare", back_populates="video")

class Tag(Base):
    __tablename__ = "tags"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True, nullable=False)
    color = Column(String, nullable=False)  # Hex color code
    description = Column(Text, nullable=True)
    created_date = Column(DateTime(timezone=True), server_default=func.now())
    usage_count = Column(Integer, default=0)
    
    # Relationships
    videos = relationship("Video", secondary=video_tags, back_populates="tags")

class ProcessingJob(Base):
    __tablename__ = "processing_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    job_type = Column(String, nullable=False)  # transcription, tagging, thumbnail
    status = Column(String, default="pending")  # pending, running, completed, failed
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Relationship
    video = relationship("Video")

class Recipe(Base):
    __tablename__ = "recipes"

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False, unique=True)
    title = Column(String, nullable=True)
    description = Column(Text, nullable=True)

    # Structured recipe data
    ingredients = Column(JSON, nullable=True)  # List of ingredient objects
    instructions = Column(JSON, nullable=True)  # List of instruction steps

    # Additional recipe metadata
    prep_time = Column(String, nullable=True)  # e.g., "15 minutes"
    cook_time = Column(String, nullable=True)  # e.g., "30 minutes"
    total_time = Column(String, nullable=True)  # e.g., "45 minutes"
    servings = Column(String, nullable=True)   # e.g., "4 servings"
    difficulty = Column(String, nullable=True)  # e.g., "Easy", "Medium", "Hard"
    cuisine_type = Column(String, nullable=True)  # e.g., "Italian", "Mexican"

    # Extraction metadata
    extracted_at = Column(DateTime(timezone=True), server_default=func.now())
    extraction_confidence = Column(Float, nullable=True)  # 0.0 to 1.0

    # Relationship
    video = relationship("Video", back_populates="recipe")

class VideoShare(Base):
    __tablename__ = "video_shares"

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    permission_level = Column(Enum(PermissionLevel), nullable=False)
    shared_at = Column(DateTime(timezone=True), server_default=func.now())
    shared_by = Column(Integer, ForeignKey("users.id"), nullable=False)  # Who shared it
    expires_at = Column(DateTime(timezone=True), nullable=True)  # Optional expiration
    is_active = Column(Boolean, default=True)

    # Relationships
    video = relationship("Video", back_populates="shares")
    user = relationship("User", back_populates="shared_videos", foreign_keys=[user_id])
    sharer = relationship("User", foreign_keys=[shared_by])

class UserSession(Base):
    __tablename__ = "user_sessions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_token = Column(String, unique=True, index=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)
    is_active = Column(Boolean, default=True)
    user_agent = Column(String, nullable=True)
    ip_address = Column(String, nullable=True)

    # Relationship
    user = relationship("User", back_populates="sessions")

# Create all tables
def create_tables():
    Base.metadata.create_all(bind=engine)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
