"""
Video sharing service for managing video permissions and access control.
"""

from typing import List, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from models.database import Video, VideoShare, User, PermissionLevel
from models.schemas import VideoShareCreate, VideoShareUpdate, PermissionLevel as SchemaPermissionLevel
from utils.auth import get_user_by_id

class SharingService:
    """Service for managing video sharing and permissions."""
    
    @staticmethod
    def get_user_permission(db: Session, video_id: int, user_id: int) -> Optional[PermissionLevel]:
        """Get the user's permission level for a video."""
        # Check if user is the owner
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            return None
        
        if video.user_id == user_id:
            return PermissionLevel.OWNER
        
        # Check if user has explicit share permission
        share = db.query(VideoShare).filter(
            VideoShare.video_id == video_id,
            VideoShare.user_id == user_id,
            VideoShare.is_active == True
        ).first()
        
        if share:
            # Check if share has expired
            if share.expires_at and share.expires_at < datetime.utcnow():
                return None
            return share.permission_level
        
        return None
    
    @staticmethod
    def can_user_access_video(db: Session, video_id: int, user_id: int, required_permission: PermissionLevel) -> bool:
        """Check if user has the required permission for a video."""
        user_permission = SharingService.get_user_permission(db, video_id, user_id)
        if not user_permission:
            return False
        
        # Permission hierarchy: OWNER > COMMENT > DOWNLOAD > VIEW
        permission_hierarchy = {
            PermissionLevel.VIEW: 1,
            PermissionLevel.DOWNLOAD: 2,
            PermissionLevel.COMMENT: 3,
            PermissionLevel.OWNER: 4
        }
        
        user_level = permission_hierarchy.get(user_permission, 0)
        required_level = permission_hierarchy.get(required_permission, 0)
        
        return user_level >= required_level
    
    @staticmethod
    def share_video(db: Session, video_id: int, target_user_id: int, permission_level: PermissionLevel, 
                   shared_by_user_id: int, expires_at: Optional[datetime] = None) -> VideoShare:
        """Share a video with another user."""
        # Verify the video exists
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # Verify the sharer has permission to share (must be owner or have COMMENT+ permission)
        if not SharingService.can_user_access_video(db, video_id, shared_by_user_id, PermissionLevel.COMMENT):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to share this video"
            )
        
        # Verify target user exists
        target_user = get_user_by_id(db, target_user_id)
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Target user not found"
            )
        
        # Check if share already exists
        existing_share = db.query(VideoShare).filter(
            VideoShare.video_id == video_id,
            VideoShare.user_id == target_user_id
        ).first()
        
        if existing_share:
            # Update existing share
            existing_share.permission_level = permission_level
            existing_share.shared_by = shared_by_user_id
            existing_share.expires_at = expires_at
            existing_share.is_active = True
            existing_share.shared_at = datetime.utcnow()
            db.commit()
            db.refresh(existing_share)
            return existing_share
        else:
            # Create new share
            new_share = VideoShare(
                video_id=video_id,
                user_id=target_user_id,
                permission_level=permission_level,
                shared_by=shared_by_user_id,
                expires_at=expires_at,
                is_active=True
            )
            db.add(new_share)
            db.commit()
            db.refresh(new_share)
            return new_share
    
    @staticmethod
    def update_video_share(db: Session, share_id: int, share_update: VideoShareUpdate, 
                          user_id: int) -> VideoShare:
        """Update an existing video share."""
        share = db.query(VideoShare).filter(VideoShare.id == share_id).first()
        if not share:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Share not found"
            )
        
        # Verify user has permission to update (must be owner or original sharer)
        video = db.query(Video).filter(Video.id == share.video_id).first()
        if video.user_id != user_id and share.shared_by != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to update this share"
            )
        
        # Update share fields
        if share_update.permission_level is not None:
            share.permission_level = share_update.permission_level
        if share_update.expires_at is not None:
            share.expires_at = share_update.expires_at
        if share_update.is_active is not None:
            share.is_active = share_update.is_active
        
        db.commit()
        db.refresh(share)
        return share
    
    @staticmethod
    def revoke_video_share(db: Session, share_id: int, user_id: int) -> bool:
        """Revoke a video share."""
        share = db.query(VideoShare).filter(VideoShare.id == share_id).first()
        if not share:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Share not found"
            )
        
        # Verify user has permission to revoke (must be owner or original sharer)
        video = db.query(Video).filter(Video.id == share.video_id).first()
        if video.user_id != user_id and share.shared_by != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to revoke this share"
            )
        
        share.is_active = False
        db.commit()
        return True
    
    @staticmethod
    def get_video_shares(db: Session, video_id: int, user_id: int) -> List[VideoShare]:
        """Get all shares for a video (owner only)."""
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        if video.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to view shares for this video"
            )
        
        return db.query(VideoShare).filter(VideoShare.video_id == video_id).all()
    
    @staticmethod
    def get_user_shared_videos(db: Session, user_id: int) -> List[VideoShare]:
        """Get all videos shared with a user."""
        return db.query(VideoShare).filter(
            VideoShare.user_id == user_id,
            VideoShare.is_active == True
        ).all()
