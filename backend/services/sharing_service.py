"""
Video sharing service for managing video permissions and access control.
"""

from typing import List, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from models.database import Video, VideoShare, User, PermissionLevel, SharingActivity, VideoAccessLog
from models.schemas import (
    VideoShareCreate, VideoShareUpdate, PermissionLevel as SchemaPermissionLevel,
    BulkShareRequest, SharingActivityCreate, VideoAccessLogCreate
)
from utils.auth import get_user_by_id

class SharingService:
    """Service for managing video sharing and permissions."""
    
    @staticmethod
    def get_user_permission(db: Session, video_id: int, user_id: int) -> Optional[PermissionLevel]:
        """Get the user's permission level for a video."""
        # Check if user is the owner
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            return None
        
        if video.user_id == user_id:
            return PermissionLevel.OWNER
        
        # Check if user has explicit share permission
        share = db.query(VideoShare).filter(
            VideoShare.video_id == video_id,
            VideoShare.user_id == user_id,
            VideoShare.is_active == True
        ).first()
        
        if share:
            # Check if share has expired
            if share.expires_at and share.expires_at < datetime.utcnow():
                return None
            return share.permission_level
        
        return None
    
    @staticmethod
    def can_user_access_video(db: Session, video_id: int, user_id: int, required_permission: PermissionLevel) -> bool:
        """Check if user has the required permission for a video."""
        user_permission = SharingService.get_user_permission(db, video_id, user_id)
        if not user_permission:
            return False
        
        # Permission hierarchy: OWNER > COMMENT > DOWNLOAD > VIEW
        permission_hierarchy = {
            PermissionLevel.VIEW: 1,
            PermissionLevel.DOWNLOAD: 2,
            PermissionLevel.COMMENT: 3,
            PermissionLevel.OWNER: 4
        }
        
        user_level = permission_hierarchy.get(user_permission, 0)
        required_level = permission_hierarchy.get(required_permission, 0)
        
        return user_level >= required_level
    
    @staticmethod
    def share_video(db: Session, video_id: int, target_user_id: int, permission_level: PermissionLevel, 
                   shared_by_user_id: int, expires_at: Optional[datetime] = None) -> VideoShare:
        """Share a video with another user."""
        # Verify the video exists
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # Verify the sharer has permission to share (must be owner or have COMMENT+ permission)
        if not SharingService.can_user_access_video(db, video_id, shared_by_user_id, PermissionLevel.COMMENT):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to share this video"
            )
        
        # Verify target user exists
        target_user = get_user_by_id(db, target_user_id)
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Target user not found"
            )
        
        # Check if share already exists
        existing_share = db.query(VideoShare).filter(
            VideoShare.video_id == video_id,
            VideoShare.user_id == target_user_id
        ).first()
        
        if existing_share:
            # Update existing share
            existing_share.permission_level = permission_level
            existing_share.shared_by = shared_by_user_id
            existing_share.expires_at = expires_at
            existing_share.is_active = True
            existing_share.shared_at = datetime.utcnow()

            # Create sharing activity for update
            activity = SharingActivity(
                video_id=video_id,
                user_id=shared_by_user_id,
                action_type="update",
                target_user_id=target_user_id,
                permission_level=permission_level,
                activity_metadata={"share_id": existing_share.id}
            )
            db.add(activity)

            db.commit()
            db.refresh(existing_share)
            return existing_share
        else:
            # Create new share
            new_share = VideoShare(
                video_id=video_id,
                user_id=target_user_id,
                permission_level=permission_level,
                shared_by=shared_by_user_id,
                expires_at=expires_at,
                is_active=True
            )
            db.add(new_share)
            db.flush()  # Get the ID

            # Create sharing activity for creation
            activity = SharingActivity(
                video_id=video_id,
                user_id=shared_by_user_id,
                action_type="create",
                target_user_id=target_user_id,
                permission_level=permission_level,
                activity_metadata={"share_id": new_share.id}
            )
            db.add(activity)

            db.commit()
            db.refresh(new_share)
            return new_share
    
    @staticmethod
    def update_video_share(db: Session, share_id: int, share_update: VideoShareUpdate, 
                          user_id: int) -> VideoShare:
        """Update an existing video share."""
        share = db.query(VideoShare).filter(VideoShare.id == share_id).first()
        if not share:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Share not found"
            )
        
        # Verify user has permission to update (must be owner or original sharer)
        video = db.query(Video).filter(Video.id == share.video_id).first()
        if video.user_id != user_id and share.shared_by != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to update this share"
            )
        
        # Update share fields
        if share_update.permission_level is not None:
            share.permission_level = share_update.permission_level
        if share_update.expires_at is not None:
            share.expires_at = share_update.expires_at
        if share_update.is_active is not None:
            share.is_active = share_update.is_active
        
        db.commit()
        db.refresh(share)
        return share
    
    @staticmethod
    def revoke_video_share(db: Session, share_id: int, user_id: int) -> bool:
        """Revoke a video share."""
        share = db.query(VideoShare).filter(VideoShare.id == share_id).first()
        if not share:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Share not found"
            )
        
        # Verify user has permission to revoke (must be owner or original sharer)
        video = db.query(Video).filter(Video.id == share.video_id).first()
        if video.user_id != user_id and share.shared_by != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to revoke this share"
            )
        
        share.is_active = False

        # Create sharing activity for revocation
        activity = SharingActivity(
            video_id=share.video_id,
            user_id=user_id,
            action_type="delete",
            target_user_id=share.user_id,
            permission_level=share.permission_level,
            activity_metadata={"share_id": share_id}
        )
        db.add(activity)

        db.commit()
        return True
    
    @staticmethod
    def get_video_shares(db: Session, video_id: int, user_id: int) -> List[VideoShare]:
        """Get all shares for a video (owner only)."""
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        if video.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to view shares for this video"
            )
        
        return db.query(VideoShare).filter(VideoShare.video_id == video_id).all()
    
    @staticmethod
    def get_user_shared_videos(db: Session, user_id: int) -> List[VideoShare]:
        """Get all videos shared with a user."""
        return db.query(VideoShare).filter(
            VideoShare.user_id == user_id,
            VideoShare.is_active == True
        ).all()

    @staticmethod
    def track_video_access(db: Session, share_id: int, user_id: int,
                          access_type: str, ip_address: Optional[str] = None,
                          user_agent: Optional[str] = None) -> bool:
        """Track video access and update analytics."""
        try:
            # Get the share
            share = db.query(VideoShare).filter(VideoShare.id == share_id).first()
            if not share:
                return False

            # Create access log
            access_log = VideoAccessLog(
                share_id=share_id,
                user_id=user_id,
                access_type=access_type,
                ip_address=ip_address,
                user_agent=user_agent
            )
            db.add(access_log)

            # Update share analytics
            share.access_count = (share.access_count or 0) + 1
            share.last_accessed_at = datetime.utcnow()

            # Create sharing activity
            activity = SharingActivity(
                video_id=share.video_id,
                user_id=user_id,
                action_type="access",
                target_user_id=share.user_id,
                permission_level=share.permission_level,
                activity_metadata={"access_type": access_type, "share_id": share_id}
            )
            db.add(activity)

            db.commit()
            return True
        except Exception as e:
            db.rollback()
            print(f"Error tracking video access: {e}")
            return False

    @staticmethod
    def bulk_share_videos(db: Session, video_ids: List[int], user_ids: List[int],
                         permission_level: PermissionLevel, shared_by_user_id: int,
                         expires_at: Optional[datetime] = None) -> dict:
        """Share multiple videos with multiple users."""
        success_count = 0
        failed_count = 0
        errors = []
        created_shares = []

        try:
            for video_id in video_ids:
                for user_id in user_ids:
                    try:
                        # Skip sharing with self
                        if user_id == shared_by_user_id:
                            continue

                        share = SharingService.share_video(
                            db=db,
                            video_id=video_id,
                            target_user_id=user_id,
                            permission_level=permission_level,
                            shared_by_user_id=shared_by_user_id,
                            expires_at=expires_at
                        )
                        created_shares.append(share)
                        success_count += 1

                        # Create sharing activity
                        activity = SharingActivity(
                            video_id=video_id,
                            user_id=shared_by_user_id,
                            action_type="create",
                            target_user_id=user_id,
                            permission_level=permission_level,
                            activity_metadata={"bulk_operation": True}
                        )
                        db.add(activity)

                    except Exception as e:
                        failed_count += 1
                        errors.append(f"Failed to share video {video_id} with user {user_id}: {str(e)}")

            db.commit()

        except Exception as e:
            db.rollback()
            errors.append(f"Bulk operation failed: {str(e)}")

        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "errors": errors,
            "created_shares": created_shares
        }

    @staticmethod
    def get_sharing_analytics(db: Session, user_id: int, start_date: Optional[datetime] = None,
                             end_date: Optional[datetime] = None, video_id: Optional[int] = None) -> dict:
        """Get sharing analytics for a user."""
        from sqlalchemy import func, and_

        # Base query filters
        filters = [VideoShare.shared_by == user_id]

        if start_date:
            filters.append(VideoShare.shared_at >= start_date)
        if end_date:
            filters.append(VideoShare.shared_at <= end_date)
        if video_id:
            filters.append(VideoShare.video_id == video_id)

        # Total shares
        total_shares = db.query(VideoShare).filter(and_(*filters)).count()

        # Total accesses
        total_accesses = db.query(func.sum(VideoShare.access_count)).filter(and_(*filters)).scalar() or 0

        # Unique users shared with
        unique_users = db.query(func.count(func.distinct(VideoShare.user_id))).filter(and_(*filters)).scalar() or 0

        # Most shared videos
        most_shared_videos = db.query(
            VideoShare.video_id,
            func.count(VideoShare.id).label('share_count'),
            func.sum(VideoShare.access_count).label('total_accesses')
        ).filter(and_(*filters)).group_by(VideoShare.video_id).order_by(
            func.count(VideoShare.id).desc()
        ).limit(10).all()

        # Most accessed shares
        most_accessed_shares = db.query(VideoShare).filter(
            and_(*filters)
        ).order_by(VideoShare.access_count.desc()).limit(10).all()

        return {
            "total_shares": total_shares,
            "total_accesses": total_accesses,
            "unique_users_shared_with": unique_users,
            "most_shared_videos": [
                {
                    "video_id": video_id,
                    "share_count": share_count,
                    "total_accesses": total_accesses or 0
                }
                for video_id, share_count, total_accesses in most_shared_videos
            ],
            "most_accessed_shares": [
                {
                    "share_id": share.id,
                    "video_id": share.video_id,
                    "access_count": share.access_count or 0,
                    "last_accessed_at": share.last_accessed_at
                }
                for share in most_accessed_shares
            ]
        }
