#!/usr/bin/env python3
"""
Quick verification script to check if the sharing features implementation is working.
This script performs basic checks on the codebase to ensure all components are in place.
"""

import os
import sys
from pathlib import Path

def check_file_exists(file_path, description):
    """Check if a file exists and print result."""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} - NOT FOUND")
        return False

def check_content_in_file(file_path, content, description):
    """Check if specific content exists in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
            if content in file_content:
                print(f"✅ {description}")
                return True
            else:
                print(f"❌ {description} - CONTENT NOT FOUND")
                return False
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False

def main():
    """Run verification checks."""
    print("🔍 TagTok Sharing Features Implementation Verification")
    print("=" * 60)
    
    # Get the project root directory
    project_root = Path(__file__).parent
    backend_root = project_root / "backend"
    frontend_root = project_root / "frontend" / "src"
    
    all_checks_passed = True
    
    # Backend Database Models
    print("\n📊 Backend Database Models:")
    checks = [
        (backend_root / "models" / "database.py", "Database models file"),
        (backend_root / "models" / "schemas.py", "Pydantic schemas file"),
        (backend_root / "migrations" / "002_add_sharing_analytics.py", "Database migration script"),
    ]
    
    for file_path, description in checks:
        if not check_file_exists(file_path, description):
            all_checks_passed = False
    
    # Check for new model classes in database.py
    print("\n🗃️ Database Model Classes:")
    db_models_file = backend_root / "models" / "database.py"
    model_checks = [
        ("class SharingActivity", "SharingActivity model"),
        ("class VideoAccessLog", "VideoAccessLog model"),
        ("access_count = Column", "VideoShare analytics fields"),
        ("last_accessed_at = Column", "VideoShare last accessed field"),
    ]
    
    for content, description in model_checks:
        if not check_content_in_file(db_models_file, content, description):
            all_checks_passed = False
    
    # Backend Services
    print("\n🔧 Backend Services:")
    sharing_service = backend_root / "services" / "sharing_service.py"
    service_checks = [
        ("def track_video_access", "Video access tracking method"),
        ("def bulk_share_videos", "Bulk sharing method"),
        ("def get_sharing_analytics", "Analytics method"),
        ("SharingActivity", "SharingActivity import"),
        ("VideoAccessLog", "VideoAccessLog import"),
    ]
    
    for content, description in service_checks:
        if not check_content_in_file(sharing_service, content, description):
            all_checks_passed = False
    
    # Backend API Routes
    print("\n🛣️ Backend API Routes:")
    sharing_router = backend_root / "routers" / "sharing.py"
    auth_router = backend_root / "routers" / "auth.py"
    
    route_checks = [
        (sharing_router, "/shared-by-me", "Shared by me endpoint"),
        (sharing_router, "/shared-with-me", "Shared with me endpoint"),
        (sharing_router, "/bulk-share", "Bulk share endpoint"),
        (sharing_router, "/analytics", "Analytics endpoint"),
        (sharing_router, "/track-access", "Access tracking endpoint"),
        (auth_router, "/users/search", "User search endpoint"),
    ]
    
    for file_path, content, description in route_checks:
        if not check_content_in_file(file_path, content, description):
            all_checks_passed = False
    
    # Frontend Components
    print("\n⚛️ Frontend Components:")
    frontend_checks = [
        (frontend_root / "pages" / "SharedByMePage.tsx", "Shared by Me page"),
        (frontend_root / "pages" / "SharedWithMePage.tsx", "Shared with Me page"),
        (frontend_root / "components" / "BulkShareModal.tsx", "Bulk share modal"),
        (frontend_root / "components" / "SharingAnalyticsCard.tsx", "Sharing analytics card"),
    ]
    
    for file_path, description in frontend_checks:
        if not check_file_exists(file_path, description):
            all_checks_passed = False
    
    # Frontend Types and API
    print("\n📝 Frontend Types and API:")
    types_file = frontend_root / "types" / "index.ts"
    api_file = frontend_root / "utils" / "api.ts"
    
    type_checks = [
        (types_file, "interface VideoShare", "VideoShare interface"),
        (types_file, "interface BulkShareRequest", "BulkShareRequest interface"),
        (types_file, "interface SharingAnalytics", "SharingAnalytics interface"),
        (types_file, "access_count: number", "VideoShare analytics fields"),
        (api_file, "export const sharingApi", "Sharing API functions"),
        (api_file, "getSharedByMe", "Shared by me API"),
        (api_file, "getSharedWithMe", "Shared with me API"),
        (api_file, "bulkShare", "Bulk share API"),
        (api_file, "getAnalytics", "Analytics API"),
    ]
    
    for file_path, content, description in type_checks:
        if not check_content_in_file(file_path, content, description):
            all_checks_passed = False
    
    # Frontend Routing
    print("\n🧭 Frontend Routing:")
    app_file = frontend_root / "App.tsx"
    layout_file = frontend_root / "components" / "Layout.tsx"
    
    routing_checks = [
        (app_file, "SharedByMePage", "Shared by Me page import"),
        (app_file, "SharedWithMePage", "Shared with Me page import"),
        (app_file, "/shared-by-me", "Shared by Me route"),
        (app_file, "/shared-with-me", "Shared with Me route"),
        (layout_file, "Shared by Me", "Navigation item"),
        (layout_file, "Shared with Me", "Navigation item"),
    ]
    
    for file_path, content, description in routing_checks:
        if not check_content_in_file(file_path, content, description):
            all_checks_passed = False
    
    # Summary
    print("\n" + "=" * 60)
    if all_checks_passed:
        print("🎉 ALL CHECKS PASSED! Implementation appears to be complete.")
        print("\n📋 Next Steps:")
        print("1. Run the database migration: docker-compose exec backend python migrations/002_add_sharing_analytics.py")
        print("2. Restart the application: docker-compose restart")
        print("3. Test the features using the test protocol in test_sharing_features.md")
        print("4. Create test users and videos to verify functionality")
        return 0
    else:
        print("⚠️ SOME CHECKS FAILED! Please review the missing components above.")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure all files were created correctly")
        print("2. Check for syntax errors in the code")
        print("3. Verify imports and dependencies")
        print("4. Review the implementation steps")
        return 1

if __name__ == "__main__":
    sys.exit(main())
