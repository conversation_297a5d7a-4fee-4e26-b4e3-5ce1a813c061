# TagTok Multi-User System Setup Guide

This guide will help you migrate from the single-user TagTok system to the new multi-user system with authentication, video sharing, and permission management.

## 🚀 Quick Migration

For existing TagTok installations, run the automated migration script:

```bash
./scripts/migrate_to_multiuser.sh
```

This script will:
- Backup your existing database
- Install new dependencies
- Run database migrations
- Create a default admin user
- Start the updated services

## 🔐 Default Admin Account

After migration, you can login with:
- **Username**: `admin`
- **Password**: `admin123`

**⚠️ IMPORTANT**: Change this password immediately after first login!

## 🆕 New Features

### Authentication System
- JWT-based authentication
- User registration and login
- Session management
- Password hashing with bcrypt

### Video Ownership & Sharing
- Each video is owned by the user who uploaded it
- Four permission levels:
  - **View**: Can view the video
  - **Download**: Can view and download the video
  - **Comment**: Can view, download, and comment on the video
  - **Owner**: Full control over the video

### Permission-Based UI
- Interface elements show/hide based on user permissions
- Video actions menu with permission checks
- Owner information displayed on video cards

### Enhanced Recipe Extraction
- Improved Spanish keyword detection
- Better title generation for cooking videos
- Enhanced cooking term recognition

## 🛠️ Manual Setup

If you prefer to set up manually:

### 1. Update Dependencies

```bash
# Backend dependencies
cd backend
pip install python-jose[cryptography]==3.3.0 passlib[bcrypt]==1.7.4 email-validator==2.1.0

# Frontend dependencies (already included)
cd ../frontend
npm install
```

### 2. Environment Configuration

Add to your `.env` file:

```bash
# Authentication Configuration
SECRET_KEY=your-secret-key-change-this-in-production-use-openssl-rand-hex-32
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

Generate a secure secret key:
```bash
openssl rand -hex 32
```

### 3. Database Migration

```bash
cd backend
python migrations/001_add_user_system.py
```

### 4. Start Services

```bash
docker-compose up -d
```

## 📋 API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `GET /auth/me` - Get current user info
- `PUT /auth/me` - Update user profile

### Video Sharing
- `POST /sharing/videos/{video_id}/share` - Share video with user
- `GET /sharing/videos/{video_id}/shares` - Get video shares (owner only)
- `GET /sharing/shared-with-me` - Get videos shared with current user
- `PUT /sharing/shares/{share_id}` - Update share permissions
- `DELETE /sharing/shares/{share_id}` - Revoke video share

### Permission Levels
- `GET /sharing/videos/{video_id}/my-permission` - Get user's permission for video

## 🔒 Security Features

### Password Security
- Bcrypt hashing with salt
- Minimum 6 character requirement
- Secure password validation

### JWT Tokens
- Configurable expiration time
- Secure token generation
- Automatic token refresh

### Permission System
- Hierarchical permission levels
- Owner-only actions protected
- Permission checks on all video operations

## 🎯 User Workflows

### For Video Owners
1. Upload videos (automatically assigned as owner)
2. Share videos with specific users and permission levels
3. Manage shares (update permissions, revoke access)
4. Full control over owned videos

### For Shared Users
1. View videos shared with them
2. Download videos (if permission allows)
3. Comment on videos (if permission allows)
4. Respect permission boundaries

### For Administrators
1. Manage all users (superuser only)
2. View system-wide analytics
3. Moderate content and users

## 🔧 Configuration Options

### JWT Configuration
```bash
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30  # Token expiration time
```

### Database Configuration
```bash
DATABASE_URL=sqlite:///db/tagTok.db  # SQLite (default)
# DATABASE_URL=postgresql://user:pass@host:port/db  # PostgreSQL
```

### CORS Configuration
Update `backend/main.py` for production:
```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://yourdomain.com"],  # Specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## 🚨 Troubleshooting

### Migration Issues
If migration fails:
1. Check database backup in `data/db/backup_*`
2. Restore backup: `cp -r data/db/backup_*/* data/db/`
3. Check logs: `docker-compose logs backend`

### Authentication Issues
- Verify SECRET_KEY is set in `.env`
- Check token expiration settings
- Clear browser localStorage if needed

### Permission Issues
- Verify user has correct permissions
- Check video ownership
- Ensure shares are active and not expired

### Frontend Issues
- Clear browser cache
- Check API URL configuration
- Verify authentication state

## 📊 Monitoring

### Health Checks
- Backend: `http://localhost:8080/health`
- Frontend: `http://localhost:3001`

### Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
```

### Database
```bash
# Access SQLite database
sqlite3 data/db/tagTok.db

# Check users
SELECT * FROM users;

# Check video shares
SELECT * FROM video_shares;
```

## 🔄 Backup & Recovery

### Database Backup
```bash
# Create backup
cp data/db/tagTok.db data/db/tagTok_backup_$(date +%Y%m%d).db

# Restore backup
cp data/db/tagTok_backup_YYYYMMDD.db data/db/tagTok.db
```

### Full System Backup
```bash
# Backup all data
tar -czf tagtok_backup_$(date +%Y%m%d).tar.gz data/
```

## 🚀 Production Deployment

### Security Checklist
- [ ] Change default admin password
- [ ] Generate secure SECRET_KEY
- [ ] Configure HTTPS
- [ ] Set specific CORS origins
- [ ] Use strong database passwords
- [ ] Enable firewall rules
- [ ] Regular security updates

### Performance Optimization
- [ ] Use PostgreSQL for production
- [ ] Configure reverse proxy (nginx)
- [ ] Set up CDN for static files
- [ ] Monitor resource usage
- [ ] Implement log rotation

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review logs for error messages
3. Check GitHub issues
4. Create new issue with detailed information

## 🎉 Success!

Your TagTok installation now supports:
- ✅ Multi-user authentication
- ✅ Video ownership and sharing
- ✅ Permission-based access control
- ✅ Enhanced recipe extraction
- ✅ Secure user management

Enjoy your upgraded TagTok experience!
