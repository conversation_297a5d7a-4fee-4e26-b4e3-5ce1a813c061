#!/bin/bash

# TagTok Multi-User Migration Script
# This script migrates the existing single-user TagTok system to multi-user

set -e

echo "🚀 Starting TagTok Multi-User Migration..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml not found. Please run this script from the TagTok root directory."
    exit 1
fi

echo "📦 Building updated containers..."
docker-compose build --no-cache backend

echo "🛑 Stopping existing services..."
docker-compose down

echo "📊 Creating database backup..."
if [ -d "data/db" ]; then
    backup_dir="data/db/backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    cp -r data/db/* "$backup_dir/" 2>/dev/null || echo "No existing database files to backup"
    echo "✅ Database backed up to $backup_dir"
else
    echo "ℹ️  No existing database found - this appears to be a fresh installation"
fi

echo "🔧 Installing new Python dependencies..."
docker-compose run --rm backend pip install -r requirements.txt

echo "🗄️  Running database migration..."
docker-compose run --rm backend python migrations/001_add_user_system.py

if [ $? -eq 0 ]; then
    echo "✅ Database migration completed successfully!"
    echo ""
    echo "🔐 Default admin user created:"
    echo "   Username: admin"
    echo "   Password: admin123"
    echo "   ⚠️  IMPORTANT: Change this password immediately after first login!"
    echo ""
else
    echo "❌ Database migration failed!"
    echo "🔄 Restoring from backup..."
    if [ -d "$backup_dir" ]; then
        rm -rf data/db/*
        cp -r "$backup_dir"/* data/db/
        echo "✅ Database restored from backup"
    fi
    exit 1
fi

echo "🚀 Starting services..."
docker-compose up -d

echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are healthy
echo "🔍 Checking service health..."
if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "✅ Backend service is healthy"
else
    echo "⚠️  Backend service may not be ready yet. Check logs with: docker-compose logs backend"
fi

if curl -f http://localhost:3001 > /dev/null 2>&1; then
    echo "✅ Frontend service is healthy"
else
    echo "⚠️  Frontend service may not be ready yet. Check logs with: docker-compose logs frontend"
fi

echo ""
echo "🎉 TagTok Multi-User Migration Complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Open http://localhost:3001 in your browser"
echo "2. Login with admin/admin123"
echo "3. Change the admin password immediately"
echo "4. Create additional user accounts as needed"
echo "5. Test video upload and sharing functionality"
echo ""
echo "📚 Documentation:"
echo "- API docs: http://localhost:8080/docs"
echo "- Check logs: docker-compose logs -f"
echo "- Stop services: docker-compose down"
echo ""
echo "🔒 Security Notes:"
echo "- Change the SECRET_KEY in .env for production"
echo "- Use strong passwords for all user accounts"
echo "- Consider setting up HTTPS for production deployment"
echo ""
