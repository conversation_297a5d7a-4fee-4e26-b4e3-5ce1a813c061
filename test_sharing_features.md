# TagTok Sharing Features Test Protocol

## Overview
This document provides a comprehensive testing protocol for the new "Shared with Me" and "Shared by Me" video sharing features implemented in TagTok.

## Prerequisites
- TagTok application running with Docker Compose
- At least 2 user accounts created for testing sharing functionality
- Several videos uploaded to test with
- Browser developer tools available for debugging

## Test Environment Setup

### 1. Start the Application
```bash
cd /path/to/tagtok
docker-compose up -d
```

### 2. Run Database Migration
```bash
docker-compose exec backend python migrations/002_add_sharing_analytics.py
```

### 3. Create Test Users
- Create at least 3 user accounts:
  - User A (Primary tester)
  - User B (Share recipient)
  - User C (Additional recipient)

### 4. Upload Test Videos
- Upload at least 5 videos to User A's account
- Ensure videos have different titles for easy identification

## Phase 1: Backend API Testing

### 1.1 Database Schema Verification
- [ ] Verify `video_shares` table has new analytics columns:
  - `access_count` (integer, default 0)
  - `last_accessed_at` (timestamp, nullable)
- [ ] Verify new tables exist:
  - `sharing_activities`
  - `video_access_logs`
- [ ] Check database indexes are created properly

### 1.2 API Endpoint Testing

#### Shared by Me Endpoint
```bash
# Test GET /api/sharing/shared-by-me
curl -H "Authorization: Bearer <token>" \
     "http://localhost:8000/sharing/shared-by-me?page=1&limit=20"
```
- [ ] Returns paginated list of shares created by current user
- [ ] Supports search parameter
- [ ] Supports sort_by parameter
- [ ] Includes analytics fields (access_count, last_accessed_at)

#### Shared with Me Endpoint
```bash
# Test GET /api/sharing/shared-with-me
curl -H "Authorization: Bearer <token>" \
     "http://localhost:8000/sharing/shared-with-me?page=1&limit=20"
```
- [ ] Returns paginated list of shares received by current user
- [ ] Supports filter_by parameter for permission levels
- [ ] Supports search parameter

#### Bulk Share Endpoint
```bash
# Test POST /api/sharing/bulk-share
curl -X POST -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"video_ids":[1,2],"user_ids":[2,3],"permission_level":"view"}' \
     "http://localhost:8000/sharing/bulk-share"
```
- [ ] Creates multiple shares successfully
- [ ] Returns success/failure counts
- [ ] Handles errors gracefully

#### Analytics Endpoint
```bash
# Test GET /api/sharing/analytics
curl -H "Authorization: Bearer <token>" \
     "http://localhost:8000/sharing/analytics?start_date=2024-01-01&end_date=2024-12-31"
```
- [ ] Returns sharing analytics data
- [ ] Supports date range filtering
- [ ] Includes most shared videos and most accessed shares

#### Access Tracking Endpoint
```bash
# Test POST /api/sharing/shares/{share_id}/track-access
curl -X POST -H "Authorization: Bearer <token>" \
     "http://localhost:8000/sharing/shares/1/track-access?access_type=view"
```
- [ ] Tracks video access successfully
- [ ] Updates share analytics
- [ ] Creates access log entry

### 1.3 User Search Endpoint
```bash
# Test GET /api/auth/users/search
curl -H "Authorization: Bearer <token>" \
     "http://localhost:8000/auth/users/search?q=test"
```
- [ ] Returns matching users
- [ ] Excludes current user from results
- [ ] Limits results appropriately

## Phase 2: Frontend Component Testing

### 2.1 Navigation Testing
- [ ] "Shared by Me" menu item appears in navigation
- [ ] "Shared with Me" menu item appears in navigation
- [ ] Navigation items are properly highlighted when active
- [ ] Routes work correctly (/shared-by-me, /shared-with-me)

### 2.2 Shared by Me Page Testing
- [ ] Page loads without errors
- [ ] Displays list of videos shared by current user
- [ ] Search functionality works with 300ms debounce
- [ ] Sort options work (date shared, access count, last accessed)
- [ ] Pagination works correctly
- [ ] Selection mode can be toggled
- [ ] Bulk selection works (select all, individual selection)
- [ ] Share analytics are displayed (access count, last accessed)
- [ ] Permission levels are displayed with correct icons and colors
- [ ] Responsive design works on mobile, tablet, desktop

### 2.3 Shared with Me Page Testing
- [ ] Page loads without errors
- [ ] Displays list of videos shared with current user
- [ ] Search functionality works with 300ms debounce
- [ ] Permission filter works (view, download, comment, owner)
- [ ] Video thumbnails display correctly
- [ ] Permission indicators show correctly
- [ ] Click tracking works when accessing videos
- [ ] Responsive design works on mobile, tablet, desktop

### 2.4 Bulk Share Modal Testing
- [ ] Modal opens when "Share More Videos" is clicked
- [ ] User search works with minimum 2 characters
- [ ] User selection works (checkboxes, visual feedback)
- [ ] Permission level selection works
- [ ] Expiration date picker works
- [ ] Form validation works (requires at least one user)
- [ ] Bulk sharing submits successfully
- [ ] Loading states display correctly
- [ ] Error handling works
- [ ] Modal closes after successful submission

### 2.5 Analytics Integration Testing
- [ ] Sharing analytics card appears on Analytics page
- [ ] Analytics data loads correctly
- [ ] Stats cards display correct values
- [ ] Most shared videos list displays
- [ ] Most accessed shares list displays
- [ ] Empty state displays when no sharing activity
- [ ] Time range selector affects sharing analytics

## Phase 3: Integration Testing

### 3.1 End-to-End Sharing Workflow
1. **User A shares video with User B**
   - [ ] Share creation works
   - [ ] User B receives access
   - [ ] Share appears in User A's "Shared by Me"
   - [ ] Share appears in User B's "Shared with Me"

2. **User B accesses shared video**
   - [ ] Video is accessible with correct permissions
   - [ ] Access is tracked automatically
   - [ ] Analytics update correctly

3. **User A views analytics**
   - [ ] Access count increases
   - [ ] Last accessed time updates
   - [ ] Analytics page shows updated data

### 3.2 Permission Level Testing
- [ ] VIEW permission: User can view but not download
- [ ] DOWNLOAD permission: User can view and download
- [ ] COMMENT permission: User can view, download, and comment
- [ ] Permission restrictions are enforced

### 3.3 Bulk Operations Testing
- [ ] Bulk share multiple videos with multiple users
- [ ] Bulk revoke shares (if implemented)
- [ ] Error handling for partial failures
- [ ] Progress indication during bulk operations

## Phase 4: Performance Testing

### 4.1 Database Performance
- [ ] Sharing queries perform well with large datasets
- [ ] Analytics queries complete in reasonable time
- [ ] Database indexes are being used effectively

### 4.2 Frontend Performance
- [ ] Pages load quickly
- [ ] Search debouncing works correctly
- [ ] Large lists are handled efficiently
- [ ] No memory leaks in React components

## Phase 5: Error Handling Testing

### 5.1 Backend Error Handling
- [ ] Invalid share IDs return 404
- [ ] Unauthorized access returns 403
- [ ] Malformed requests return 400
- [ ] Database errors are handled gracefully

### 5.2 Frontend Error Handling
- [ ] Network errors display user-friendly messages
- [ ] Loading states prevent multiple submissions
- [ ] Form validation provides clear feedback
- [ ] Retry mechanisms work for failed requests

## Phase 6: Security Testing

### 6.1 Authorization Testing
- [ ] Users can only see their own shares in "Shared by Me"
- [ ] Users can only see shares directed to them in "Shared with Me"
- [ ] Permission levels are properly enforced
- [ ] Share access cannot be bypassed

### 6.2 Data Validation Testing
- [ ] Input sanitization prevents XSS
- [ ] SQL injection protection works
- [ ] File access is properly restricted
- [ ] User enumeration is prevented

## Phase 7: Mobile Responsiveness Testing

### 7.1 Mobile Layout Testing
- [ ] Navigation works on mobile devices
- [ ] Grid layouts adapt to screen size (1 column on mobile)
- [ ] Touch interactions work properly
- [ ] Modal dialogs are mobile-friendly

### 7.2 Tablet Layout Testing
- [ ] Grid layouts use 2 columns on tablets
- [ ] Touch and mouse interactions both work
- [ ] Navigation remains accessible

## Success Criteria

### Must Pass
- [ ] All API endpoints return correct data
- [ ] All frontend components render without errors
- [ ] Basic sharing workflow works end-to-end
- [ ] Permission system is properly enforced
- [ ] Analytics tracking works correctly

### Should Pass
- [ ] Search and filtering work correctly
- [ ] Bulk operations complete successfully
- [ ] Mobile responsiveness is good
- [ ] Performance is acceptable
- [ ] Error handling is user-friendly

### Nice to Have
- [ ] Advanced analytics features work
- [ ] Complex bulk operations handle edge cases
- [ ] Accessibility features work
- [ ] Advanced permission scenarios work

## Test Execution Checklist

- [ ] All backend tests pass
- [ ] All frontend tests pass
- [ ] Integration tests pass
- [ ] Performance tests pass
- [ ] Security tests pass
- [ ] Mobile tests pass
- [ ] No console errors in browser
- [ ] No server errors in logs
- [ ] Database migrations complete successfully
- [ ] All success criteria met

## Notes and Issues

Use this section to document any issues found during testing:

1. **Issue**: [Description]
   - **Severity**: High/Medium/Low
   - **Steps to Reproduce**: [Steps]
   - **Expected**: [Expected behavior]
   - **Actual**: [Actual behavior]
   - **Status**: Open/Fixed/Deferred

## Conclusion

This comprehensive test protocol ensures that all sharing features work correctly across different scenarios, devices, and user interactions. Complete all phases before considering the implementation ready for production use.
