import React, { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  XMarkIcon,
  ShareIcon,
  UserIcon,
  MagnifyingGlassIcon,
  CheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

import { sharingApi, userApi } from '../utils/api';
import { PermissionLevel, BulkShareRequest } from '../types';
import LoadingSpinner from './LoadingSpinner';

interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
}

interface BulkShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoIds: number[];
  videoTitles: string[];
}

const BulkShareModal: React.FC<BulkShareModalProps> = ({
  isOpen,
  onClose,
  videoIds,
  videoTitles
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<Set<number>>(new Set());
  const [permissionLevel, setPermissionLevel] = useState<PermissionLevel>(PermissionLevel.VIEW);
  const [expiresAt, setExpiresAt] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const queryClient = useQueryClient();

  // Debounced user search
  const [debouncedSearch, setDebouncedSearch] = useState('');
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchTerm);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Search users
  useEffect(() => {
    const searchUsers = async () => {
      if (debouncedSearch.length < 2) {
        setUsers([]);
        return;
      }

      setIsLoadingUsers(true);
      try {
        const searchResults = await userApi.search(debouncedSearch);
        setUsers(searchResults);
      } catch (error) {
        console.error('Failed to search users:', error);
        setUsers([]);
      } finally {
        setIsLoadingUsers(false);
      }
    };

    searchUsers();
  }, [debouncedSearch]);

  const bulkShareMutation = useMutation({
    mutationFn: (request: BulkShareRequest) => sharingApi.bulkShare(request),
    onSuccess: (response) => {
      toast.success(`Successfully shared with ${response.success_count} users`);
      if (response.failed_count > 0) {
        toast.error(`Failed to share with ${response.failed_count} users`);
      }
      queryClient.invalidateQueries({ queryKey: ['shared-by-me'] });
      onClose();
      resetForm();
    },
    onError: (error) => {
      toast.error('Failed to share videos');
      console.error('Bulk share error:', error);
    },
  });

  const resetForm = () => {
    setSearchTerm('');
    setSelectedUsers(new Set());
    setPermissionLevel(PermissionLevel.VIEW);
    setExpiresAt('');
    setUsers([]);
  };

  const handleUserToggle = (userId: number) => {
    const newSelected = new Set(selectedUsers);
    if (newSelected.has(userId)) {
      newSelected.delete(userId);
    } else {
      newSelected.add(userId);
    }
    setSelectedUsers(newSelected);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedUsers.size === 0) {
      toast.error('Please select at least one user');
      return;
    }

    const request: BulkShareRequest = {
      video_ids: videoIds,
      user_ids: Array.from(selectedUsers),
      permission_level: permissionLevel,
      expires_at: expiresAt || undefined,
    };

    bulkShareMutation.mutate(request);
  };

  const getPermissionDescription = (permission: PermissionLevel) => {
    switch (permission) {
      case PermissionLevel.VIEW:
        return 'Can view the video';
      case PermissionLevel.DOWNLOAD:
        return 'Can view and download the video';
      case PermissionLevel.COMMENT:
        return 'Can view, download, and comment on the video';
      case PermissionLevel.OWNER:
        return 'Full access including sharing permissions';
      default:
        return '';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <ShareIcon className="h-6 w-6 text-primary-600 dark:text-primary-400 mr-2" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Share Videos
                </h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Video List */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Sharing {videoIds.length} video{videoIds.length !== 1 ? 's' : ''}:
              </h4>
              <div className="max-h-20 overflow-y-auto bg-gray-50 dark:bg-gray-700 rounded-md p-2">
                {videoTitles.map((title, index) => (
                  <div key={index} className="text-sm text-gray-600 dark:text-gray-400 truncate">
                    • {title}
                  </div>
                ))}
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {/* User Search */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Search Users
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search by username or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>

              {/* User List */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Select Users ({selectedUsers.size} selected)
                </label>
                <div className="max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md">
                  {isLoadingUsers ? (
                    <div className="flex justify-center py-4">
                      <LoadingSpinner size="sm" />
                    </div>
                  ) : users.length > 0 ? (
                    users.map((user) => (
                      <div
                        key={user.id}
                        className="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-600 last:border-b-0"
                      >
                        <input
                          type="checkbox"
                          checked={selectedUsers.has(user.id)}
                          onChange={() => handleUserToggle(user.id)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded"
                        />
                        <div className="ml-3 flex-1">
                          <div className="flex items-center">
                            <UserIcon className="h-4 w-4 text-gray-400 mr-2" />
                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                              {user.username}
                            </span>
                          </div>
                          {user.full_name && (
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {user.full_name}
                            </p>
                          )}
                        </div>
                        {selectedUsers.has(user.id) && (
                          <CheckIcon className="h-4 w-4 text-green-500" />
                        )}
                      </div>
                    ))
                  ) : searchTerm.length >= 2 ? (
                    <div className="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
                      No users found
                    </div>
                  ) : (
                    <div className="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
                      Type at least 2 characters to search
                    </div>
                  )}
                </div>
              </div>

              {/* Permission Level */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Permission Level
                </label>
                <select
                  value={permissionLevel}
                  onChange={(e) => setPermissionLevel(e.target.value as PermissionLevel)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value={PermissionLevel.VIEW}>View Only</option>
                  <option value={PermissionLevel.DOWNLOAD}>Download</option>
                  <option value={PermissionLevel.COMMENT}>Comment</option>
                </select>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {getPermissionDescription(permissionLevel)}
                </p>
              </div>

              {/* Expiration Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Expiration Date (Optional)
                </label>
                <input
                  type="datetime-local"
                  value={expiresAt}
                  onChange={(e) => setExpiresAt(e.target.value)}
                  min={new Date().toISOString().slice(0, 16)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={selectedUsers.size === 0 || bulkShareMutation.isPending}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {bulkShareMutation.isPending ? (
                    <div className="flex items-center">
                      <LoadingSpinner size="sm" />
                      <span className="ml-2">Sharing...</span>
                    </div>
                  ) : (
                    `Share with ${selectedUsers.size} user${selectedUsers.size !== 1 ? 's' : ''}`
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkShareModal;
