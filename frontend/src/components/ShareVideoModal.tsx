import React, { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  XMarkIcon,
  ShareIcon,
  UserIcon,
  MagnifyingGlassIcon,
  CheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

import { sharingApi, userApi } from '../utils/api';
import { Video, PermissionLevel } from '../types';
import LoadingSpinner from './LoadingSpinner';

interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
}

interface ShareVideoModalProps {
  video: Video;
  isOpen: boolean;
  onClose: () => void;
}

const ShareVideoModal: React.FC<ShareVideoModalProps> = ({
  video,
  isOpen,
  onClose
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<Set<number>>(new Set());
  const [permissionLevel, setPermissionLevel] = useState<PermissionLevel>(PermissionLevel.VIEW);
  const [expiresAt, setExpiresAt] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const queryClient = useQueryClient();

  // Debounced user search
  const [debouncedSearch, setDebouncedSearch] = useState('');
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchTerm);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Search users
  useEffect(() => {
    const searchUsers = async () => {
      if (debouncedSearch.length < 2) {
        setUsers([]);
        return;
      }

      setIsLoadingUsers(true);
      try {
        const searchResults = await userApi.search(debouncedSearch);
        setUsers(searchResults);
      } catch (error) {
        console.error('Failed to search users:', error);
        setUsers([]);
      } finally {
        setIsLoadingUsers(false);
      }
    };

    searchUsers();
  }, [debouncedSearch]);

  const shareVideoMutation = useMutation({
    mutationFn: async () => {
      const userIds = Array.from(selectedUsers);
      const sharePromises = userIds.map(userId =>
        sharingApi.shareVideoWithUser(video.id, userId, permissionLevel, expiresAt || undefined)
      );
      return Promise.all(sharePromises);
    },
    onSuccess: () => {
      toast.success(`Video shared with ${selectedUsers.size} user${selectedUsers.size !== 1 ? 's' : ''}`);
      queryClient.invalidateQueries({ queryKey: ['shared-by-me'] });
      queryClient.invalidateQueries({ queryKey: ['video-shares', video.id] });
      onClose();
      resetForm();
    },
    onError: (error) => {
      toast.error('Failed to share video');
      console.error('Share video error:', error);
    },
  });

  const resetForm = () => {
    setSearchTerm('');
    setSelectedUsers(new Set());
    setPermissionLevel(PermissionLevel.VIEW);
    setExpiresAt('');
    setUsers([]);
  };

  const handleUserToggle = (userId: number) => {
    const newSelected = new Set(selectedUsers);
    if (newSelected.has(userId)) {
      newSelected.delete(userId);
    } else {
      newSelected.add(userId);
    }
    setSelectedUsers(newSelected);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedUsers.size === 0) {
      toast.error('Please select at least one user');
      return;
    }

    shareVideoMutation.mutate();
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <ShareIcon className="h-6 w-6 text-primary-600 dark:text-primary-400 mr-3" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Share Video</h2>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Video Info */}
          <div className="mb-6">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Sharing: <span className="font-medium text-gray-900 dark:text-white">{video.title || video.original_filename}</span>
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* User Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Share with user
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search by email or name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              {/* User Search Results */}
              {searchTerm.length >= 2 && (
                <div className="mt-2 border border-gray-200 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 max-h-48 overflow-y-auto">
                  {isLoadingUsers ? (
                    <div className="p-4 text-center">
                      <LoadingSpinner size="sm" />
                    </div>
                  ) : users.length > 0 ? (
                    users.map((user) => (
                      <div
                        key={user.id}
                        className="flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                        onClick={() => handleUserToggle(user.id)}
                      >
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <UserIcon className="h-8 w-8 text-gray-400 dark:text-gray-300" />
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {user.full_name || user.username}
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {user.email}
                            </p>
                          </div>
                        </div>
                        {selectedUsers.has(user.id) && (
                          <CheckIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                        )}
                      </div>
                    ))
                  ) : searchTerm.length >= 2 ? (
                    <div className="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
                      No users found
                    </div>
                  ) : (
                    <div className="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
                      Type at least 2 characters to search
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Selected Users */}
            {selectedUsers.size > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Selected Users ({selectedUsers.size})
                </label>
                <div className="space-y-2">
                  {Array.from(selectedUsers).map((userId) => {
                    const user = users.find(u => u.id === userId);
                    if (!user) return null;
                    return (
                      <div key={userId} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
                        <span className="text-sm text-gray-900 dark:text-white">
                          {user.full_name || user.username}
                        </span>
                        <button
                          type="button"
                          onClick={() => handleUserToggle(userId)}
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Permission Level */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Permission Level
              </label>
              <select
                value={permissionLevel}
                onChange={(e) => setPermissionLevel(e.target.value as PermissionLevel)}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              >
                <option value={PermissionLevel.VIEW}>View Only</option>
                <option value={PermissionLevel.DOWNLOAD}>View & Download</option>
                <option value={PermissionLevel.COMMENT}>View, Download & Comment</option>
              </select>
            </div>

            {/* Expiration Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Expiration Date (Optional)
              </label>
              <input
                type="datetime-local"
                value={expiresAt}
                onChange={(e) => setExpiresAt(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={selectedUsers.size === 0 || shareVideoMutation.isPending}
                className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {shareVideoMutation.isPending ? (
                  <div className="flex items-center">
                    <LoadingSpinner size="sm" />
                    <span className="ml-2">Sharing...</span>
                  </div>
                ) : (
                  `Share with ${selectedUsers.size} user${selectedUsers.size !== 1 ? 's' : ''}`
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ShareVideoModal;
