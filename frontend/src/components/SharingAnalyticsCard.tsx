import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  ShareIcon,
  EyeIcon,
  UserGroupIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

import { sharingApi } from '../utils/api';
import { SharingAnalytics } from '../types';
import LoadingSpinner from './LoadingSpinner';
import StatsCard from './StatsCard';

interface SharingAnalyticsCardProps {
  timeRange: number;
}

const SharingAnalyticsCard: React.FC<SharingAnalyticsCardProps> = ({ timeRange }) => {
  // Calculate date range
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeRange);

  // Fetch sharing analytics
  const {
    data: analytics,
    isPending: isLoading,
    error,
  } = useQuery<SharingAnalytics>({
    queryKey: ['sharing-analytics', timeRange],
    queryFn: () => sharingApi.getAnalytics({
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
    }),
  });

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Sharing Analytics</h2>
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Sharing Analytics</h2>
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">Failed to load sharing analytics</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-6">Sharing Analytics</h2>
      
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <StatsCard
          title="Total Shares"
          value={analytics.total_shares}
          icon={ShareIcon}
          color="blue"
        />
        <StatsCard
          title="Total Accesses"
          value={analytics.total_accesses}
          icon={EyeIcon}
          color="green"
        />
        <StatsCard
          title="Unique Users"
          value={analytics.unique_users_shared_with}
          icon={UserGroupIcon}
          color="purple"
        />
        <StatsCard
          title="Avg. Access Rate"
          value={analytics.total_shares > 0 ? `${(analytics.total_accesses / analytics.total_shares).toFixed(1)}x` : '0x'}
          icon={ChartBarIcon}
          color="orange"
        />
      </div>

      {/* Most Shared Videos */}
      {analytics.most_shared_videos.length > 0 && (
        <div className="mb-6">
          <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">Most Shared Videos</h3>
          <div className="space-y-2">
            {analytics.most_shared_videos.slice(0, 5).map((video, index) => (
              <div
                key={video.video_id}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="flex items-center">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400 mr-3">
                    #{index + 1}
                  </span>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      Video ID: {video.video_id}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {video.share_count} share{video.share_count !== 1 ? 's' : ''} • {video.total_accesses} access{video.total_accesses !== 1 ? 'es' : ''}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {video.share_count}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    shares
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Most Accessed Shares */}
      {analytics.most_accessed_shares.length > 0 && (
        <div>
          <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">Most Accessed Shares</h3>
          <div className="space-y-2">
            {analytics.most_accessed_shares.slice(0, 5).map((share, index) => (
              <div
                key={share.share_id}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="flex items-center">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400 mr-3">
                    #{index + 1}
                  </span>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      Video ID: {share.video_id}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {share.last_accessed_at ? 
                        `Last accessed: ${new Date(share.last_accessed_at).toLocaleDateString()}` :
                        'Never accessed'
                      }
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {share.access_count}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    accesses
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {analytics.total_shares === 0 && (
        <div className="text-center py-8">
          <ShareIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No sharing activity</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Start sharing videos to see analytics here.
          </p>
        </div>
      )}
    </div>
  );
};

export default SharingAnalyticsCard;
