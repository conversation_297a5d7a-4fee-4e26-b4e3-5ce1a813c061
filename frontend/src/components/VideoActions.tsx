import React, { useState } from 'react';
import {
  EllipsisVerticalIcon,
  PlayIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  PencilIcon,
  TrashIcon,
  ChatBubbleLeftIcon
} from '@heroicons/react/24/outline';
import { Video, PermissionLevel } from '../types';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';
import ShareVideoModal from './ShareVideoModal';

interface VideoActionsProps {
  video: Video;
  onEdit?: () => void;
  onDelete?: () => void;
  onShare?: () => void;
  onDownload?: () => void;
  className?: string;
}

const VideoActions: React.FC<VideoActionsProps> = ({
  video,
  onEdit,
  onDelete,
  onShare,
  onDownload,
  className = ''
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const { user } = useAuth();

  const hasPermission = (requiredPermission: PermissionLevel): boolean => {
    if (!video.user_permission) return false;
    
    const permissionHierarchy = {
      [PermissionLevel.VIEW]: 1,
      [PermissionLevel.DOWNLOAD]: 2,
      [PermissionLevel.COMMENT]: 3,
      [PermissionLevel.OWNER]: 4
    };
    
    const userLevel = permissionHierarchy[video.user_permission];
    const requiredLevel = permissionHierarchy[requiredPermission];
    
    return userLevel >= requiredLevel;
  };

  const isOwner = video.user_permission === PermissionLevel.OWNER;
  const canDownload = hasPermission(PermissionLevel.DOWNLOAD);
  const canComment = hasPermission(PermissionLevel.COMMENT);
  const canShare = hasPermission(PermissionLevel.COMMENT);

  const handleAction = (action: () => void, actionName: string) => {
    try {
      action();
      setIsMenuOpen(false);
    } catch (error) {
      toast.error(`Failed to ${actionName}`);
    }
  };

  const handleDownload = () => {
    if (!canDownload) {
      toast.error("You don't have permission to download this video");
      return;
    }
    
    if (onDownload) {
      handleAction(onDownload, 'download video');
    } else {
      // Default download behavior
      const downloadUrl = `/api/videos/${video.id}/download`;
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = video.original_filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleShare = () => {
    if (!canShare) {
      toast.error("You don't have permission to share this video");
      return;
    }

    if (onShare) {
      handleAction(onShare, 'share video');
    } else {
      // Open the share modal
      setShowShareModal(true);
      setIsMenuOpen(false);
    }
  };

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        className="flex items-center justify-center w-8 h-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        onClick={() => setIsMenuOpen(!isMenuOpen)}
      >
        <EllipsisVerticalIcon className="w-5 h-5" />
      </button>

      {isMenuOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsMenuOpen(false)}
          />
          
          {/* Menu */}
          <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-20">
            <div className="py-1">
              {/* Always available - View */}
              <button
                onClick={() => {
                  window.location.href = `/video/${video.id}`;
                  setIsMenuOpen(false);
                }}
                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <PlayIcon className="w-4 h-4 mr-3" />
                View Video
              </button>

              {/* Download - requires DOWNLOAD permission */}
              {canDownload && (
                <button
                  onClick={handleDownload}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <ArrowDownTrayIcon className="w-4 h-4 mr-3" />
                  Download
                </button>
              )}

              {/* Share - requires COMMENT permission */}
              {canShare && (
                <button
                  onClick={handleShare}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <ShareIcon className="w-4 h-4 mr-3" />
                  Share
                </button>
              )}

              {/* Comment - requires COMMENT permission */}
              {canComment && (
                <button
                  onClick={() => {
                    // Navigate to video detail page with comments section
                    window.location.href = `/video/${video.id}#comments`;
                    setIsMenuOpen(false);
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <ChatBubbleLeftIcon className="w-4 h-4 mr-3" />
                  Comment
                </button>
              )}

              {/* Owner-only actions */}
              {isOwner && (
                <>
                  <div className="border-t border-gray-200 dark:border-gray-600 my-1" />
                  
                  {onEdit && (
                    <button
                      onClick={() => handleAction(onEdit, 'edit video')}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <PencilIcon className="w-4 h-4 mr-3" />
                      Edit
                    </button>
                  )}

                  {onDelete && (
                    <button
                      onClick={() => handleAction(onDelete, 'delete video')}
                      className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
                    >
                      <TrashIcon className="w-4 h-4 mr-3" />
                      Delete
                    </button>
                  )}
                </>
              )}
            </div>

            {/* Permission indicator */}
            <div className="border-t border-gray-200 dark:border-gray-600 px-4 py-2">
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Permission: <span className="font-medium capitalize">{video.user_permission}</span>
                {video.owner_username && (
                  <div>Owner: {video.owner_username}</div>
                )}
              </div>
            </div>
          </div>
        </>
      )}

      {/* Share Video Modal */}
      <ShareVideoModal
        video={video}
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
      />
    </div>
  );
};

export default VideoActions;
