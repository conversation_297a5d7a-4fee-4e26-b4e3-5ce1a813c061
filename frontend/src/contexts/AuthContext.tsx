import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { api } from '../utils/api';

export interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  last_login?: string;
}

export interface AuthToken {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string, fullName?: string) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  console.log('🔐 AuthProvider: Component initializing...');
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && !!token;

  // Load auth state from localStorage on mount
  useEffect(() => {
    const loadAuthState = async () => {
      console.log('AuthContext: Starting authentication check...');
      try {
        const storedToken = localStorage.getItem('auth_token');
        const storedUser = localStorage.getItem('auth_user');

        console.log('AuthContext: Stored token exists:', !!storedToken);
        console.log('AuthContext: Stored user exists:', !!storedUser);

        if (storedToken && storedUser) {
          try {
            const parsedUser = JSON.parse(storedUser);

            // Verify token is still valid by making a request to /auth/me
            console.log('AuthContext: Verifying token with backend...');
            const response = await api.get('/auth/me', {
              headers: {
                Authorization: `Bearer ${storedToken}`
              }
            });

            console.log('AuthContext: Token verified, user authenticated');
            setToken(storedToken);
            setUser(response.data);
          } catch (error) {
            console.log('AuthContext: Token invalid or verification failed, clearing stored data');
            // Token is invalid, clear stored data
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_user');
            setToken(null);
            setUser(null);
          }
        } else {
          console.log('AuthContext: No stored credentials found');
        }
      } catch (error) {
        console.error('AuthContext: Error loading auth state:', error);
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
      } finally {
        console.log('AuthContext: Authentication check complete, setting isLoading to false');
        setIsLoading(false);
      }
    };

    loadAuthState();
  }, []);

  // Update API default headers when token changes
  useEffect(() => {
    if (token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete api.defaults.headers.common['Authorization'];
    }
  }, [token]);

  const login = async (username: string, password: string): Promise<void> => {
    try {
      const response = await api.post<AuthToken>('/auth/login', {
        username,
        password
      });

      const { access_token, user: userData } = response.data;

      setToken(access_token);
      setUser(userData);

      // Store in localStorage
      localStorage.setItem('auth_token', access_token);
      localStorage.setItem('auth_user', JSON.stringify(userData));
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Login failed');
    }
  };

  const register = async (
    username: string, 
    email: string, 
    password: string, 
    fullName?: string
  ): Promise<void> => {
    try {
      const response = await api.post<User>('/auth/register', {
        username,
        email,
        password,
        full_name: fullName
      });

      // After successful registration, automatically log in
      await login(username, password);
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Registration failed');
    }
  };

  const logout = async (): Promise<void> => {
    try {
      // Call logout endpoint to invalidate server-side sessions
      if (token) {
        await api.post('/auth/logout');
      }
    } catch (error) {
      // Continue with logout even if server request fails
      console.error('Error during server logout:', error);
    } finally {
      // Clear local state
      setToken(null);
      setUser(null);
      
      // Clear localStorage
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
    }
  };

  const updateUser = async (userData: Partial<User>): Promise<void> => {
    try {
      const response = await api.put<User>('/auth/me', userData);
      
      setUser(response.data);
      localStorage.setItem('auth_user', JSON.stringify(response.data));
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to update user');
    }
  };

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    updateUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
