import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  ShareIcon, 
  EyeIcon, 
  ArrowDownTrayIcon,
  ChatBubbleLeftIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';

import { sharingApi, videoApi } from '../utils/api';
import { VideoShare, Video, PermissionLevel } from '../types';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const SharedWithMePage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState('');
  const [page, setPage] = useState(1);
  const navigate = useNavigate();

  // Debounced search
  const [debouncedSearch, setDebouncedSearch] = useState('');
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchTerm);
      setPage(1); // Reset to first page on search
    }, 300);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch shared videos
  const {
    data: shares = [],
    isPending: isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['shared-with-me', page, debouncedSearch, filterBy],
    queryFn: () => sharingApi.getSharedWithMe({
      page,
      limit: 20,
      search: debouncedSearch || undefined,
      filter_by: filterBy || undefined
    }),
  });

  // Fetch video details for shares
  const videoIds = shares.map(share => share.video_id);
  const { data: videos = [] } = useQuery({
    queryKey: ['videos-for-shared-with-me', videoIds],
    queryFn: async () => {
      if (videoIds.length === 0) return [];
      const videoPromises = videoIds.map(id => videoApi.getVideo(id));
      return Promise.all(videoPromises);
    },
    enabled: videoIds.length > 0,
  });

  const getPermissionIcon = (permission: PermissionLevel) => {
    switch (permission) {
      case PermissionLevel.VIEW:
        return <EyeIcon className="h-4 w-4" />;
      case PermissionLevel.DOWNLOAD:
        return <ArrowDownTrayIcon className="h-4 w-4" />;
      case PermissionLevel.COMMENT:
        return <ChatBubbleLeftIcon className="h-4 w-4" />;
      case PermissionLevel.OWNER:
        return <ShareIcon className="h-4 w-4" />;
      default:
        return <EyeIcon className="h-4 w-4" />;
    }
  };

  const getPermissionColor = (permission: PermissionLevel) => {
    switch (permission) {
      case PermissionLevel.VIEW:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case PermissionLevel.DOWNLOAD:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case PermissionLevel.COMMENT:
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case PermissionLevel.OWNER:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getVideoForShare = (share: VideoShare): Video | undefined => {
    return videos.find(video => video.id === share.video_id);
  };

  const handleVideoClick = async (share: VideoShare) => {
    // Track access
    try {
      await sharingApi.trackAccess(share.id, 'view');
    } catch (error) {
      console.error('Failed to track access:', error);
    }
    
    // Navigate to video detail page
    navigate(`/videos/${share.video_id}`);
  };

  const canDownload = (permission: PermissionLevel) => {
    return permission === PermissionLevel.DOWNLOAD || 
           permission === PermissionLevel.COMMENT || 
           permission === PermissionLevel.OWNER;
  };

  const canComment = (permission: PermissionLevel) => {
    return permission === PermissionLevel.COMMENT || 
           permission === PermissionLevel.OWNER;
  };

  if (error) {
    return <ErrorMessage message="Failed to load shared videos" onRetry={refetch} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Shared with Me</h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            Videos that others have shared with you
          </p>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by video title or sharer name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
        <div className="relative">
          <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <select
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value)}
            className="block pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">All Permissions</option>
            <option value="view">View Only</option>
            <option value="download">Download</option>
            <option value="comment">Comment</option>
            <option value="owner">Owner</option>
          </select>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      )}

      {/* Shares Grid */}
      {!isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {shares.map((share) => {
            const video = getVideoForShare(share);
            return (
              <div
                key={share.id}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-900/20 overflow-hidden hover:shadow-lg dark:hover:shadow-gray-900/40 transition-all duration-200 cursor-pointer"
                onClick={() => handleVideoClick(share)}
              >
                {/* Video Thumbnail */}
                <div className="aspect-video bg-gray-200 dark:bg-gray-700 relative group">
                  {video?.thumbnail_path ? (
                    <img
                      src={`/api/videos/thumbnail/${video.thumbnail_path}`}
                      alt={video.title || video.original_filename}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <ShareIcon className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                  
                  {/* Play overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                    <PlayIcon className="h-12 w-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  </div>
                </div>

                {/* Share Info */}
                <div className="p-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 truncate">
                    {video?.title || video?.original_filename || 'Unknown Video'}
                  </h3>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Shared by:</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {share.sharer_username}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Your Access:</span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPermissionColor(share.permission_level)}`}>
                        {getPermissionIcon(share.permission_level)}
                        <span className="ml-1 capitalize">{share.permission_level}</span>
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Shared:</span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {formatDate(share.shared_at)}
                      </span>
                    </div>
                    
                    {video?.duration && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Duration:</span>
                        <span className="text-sm text-gray-900 dark:text-white">
                          {Math.floor(video.duration / 60)}:{(video.duration % 60).toFixed(0).padStart(2, '0')}
                        </span>
                      </div>
                    )}
                    
                    {share.expires_at && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Expires:</span>
                        <span className="text-sm text-gray-900 dark:text-white">
                          {formatDate(share.expires_at)}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Permission Indicators */}
                  <div className="mt-4 flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      <EyeIcon className="h-4 w-4 text-green-500" />
                      <span className="text-xs text-gray-500 dark:text-gray-400">View</span>
                    </div>
                    {canDownload(share.permission_level) && (
                      <div className="flex items-center space-x-1">
                        <ArrowDownTrayIcon className="h-4 w-4 text-green-500" />
                        <span className="text-xs text-gray-500 dark:text-gray-400">Download</span>
                      </div>
                    )}
                    {canComment(share.permission_level) && (
                      <div className="flex items-center space-x-1">
                        <ChatBubbleLeftIcon className="h-4 w-4 text-green-500" />
                        <span className="text-xs text-gray-500 dark:text-gray-400">Comment</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && shares.length === 0 && (
        <div className="text-center py-12">
          <ShareIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No shared videos</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {searchTerm || filterBy ? 'No videos match your search criteria.' : 'No one has shared videos with you yet.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default SharedWithMePage;
