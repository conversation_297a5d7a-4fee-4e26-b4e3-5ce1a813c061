import React, { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  ShareIcon, 
  EyeIcon, 
  ArrowDownTrayIcon,
  ChatBubbleLeftIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

import { sharingApi, videoApi } from '../utils/api';
import { VideoShare, Video, PermissionLevel } from '../types';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import BulkShareModal from '../components/BulkShareModal';
import { useAuth } from '../contexts/AuthContext';

const SharedByMePage: React.FC = () => {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('created_at');
  const [page, setPage] = useState(1);
  const [selectedShares, setSelectedShares] = useState<Set<number>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [showBulkShareModal, setShowBulkShareModal] = useState(false);
  const queryClient = useQueryClient();

  // Debounced search
  const [debouncedSearch, setDebouncedSearch] = useState('');
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchTerm);
      setPage(1); // Reset to first page on search
    }, 300);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch shared videos
  const {
    data: shares = [],
    isPending: isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['shared-by-me', page, debouncedSearch, sortBy],
    queryFn: () => sharingApi.getSharedByMe({
      page,
      limit: 20,
      search: debouncedSearch || undefined,
      sort_by: sortBy
    }),
    enabled: isAuthenticated && !authLoading, // Only fetch when authenticated
  });

  // Fetch video details for shares
  const videoIds = shares.map(share => share.video_id);
  const { data: videos = [] } = useQuery({
    queryKey: ['videos-for-shares', videoIds],
    queryFn: async () => {
      if (videoIds.length === 0) return [];
      const videoPromises = videoIds.map(id => videoApi.getVideo(id));
      return Promise.all(videoPromises);
    },
    enabled: videoIds.length > 0 && isAuthenticated && !authLoading,
  });

  const getPermissionIcon = (permission: PermissionLevel) => {
    switch (permission) {
      case PermissionLevel.VIEW:
        return <EyeIcon className="h-4 w-4" />;
      case PermissionLevel.DOWNLOAD:
        return <ArrowDownTrayIcon className="h-4 w-4" />;
      case PermissionLevel.COMMENT:
        return <ChatBubbleLeftIcon className="h-4 w-4" />;
      case PermissionLevel.OWNER:
        return <ShareIcon className="h-4 w-4" />;
      default:
        return <EyeIcon className="h-4 w-4" />;
    }
  };

  const getPermissionColor = (permission: PermissionLevel) => {
    switch (permission) {
      case PermissionLevel.VIEW:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case PermissionLevel.DOWNLOAD:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case PermissionLevel.COMMENT:
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case PermissionLevel.OWNER:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const handleSelectShare = (shareId: number) => {
    const newSelected = new Set(selectedShares);
    if (newSelected.has(shareId)) {
      newSelected.delete(shareId);
    } else {
      newSelected.add(shareId);
    }
    setSelectedShares(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedShares.size === shares.length) {
      setSelectedShares(new Set());
    } else {
      setSelectedShares(new Set(shares.map(share => share.id)));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getVideoForShare = (share: VideoShare): Video | undefined => {
    return videos.find(video => video.id === share.video_id);
  };

  if (error) {
    return <ErrorMessage message="Failed to load shared videos" onRetry={refetch} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Shared by Me</h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            Manage videos you've shared with others
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <button
            onClick={() => setIsSelectionMode(!isSelectionMode)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
            {isSelectionMode ? 'Exit Selection' : 'Select Multiple'}
          </button>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by video title or username..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="block px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="created_at">Sort by Date Shared</option>
          <option value="access_count">Sort by Access Count</option>
          <option value="last_accessed">Sort by Last Accessed</option>
        </select>
      </div>

      {/* Bulk Actions */}
      {isSelectionMode && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedShares.size === shares.length && shares.length > 0}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Select All ({selectedShares.size} selected)
                </span>
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowBulkShareModal(true)}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <ShareIcon className="h-3 w-3 mr-1" />
                Share More Videos
              </button>
              <button
                disabled={selectedShares.size === 0}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Revoke Selected
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      )}

      {/* Shares Grid */}
      {!isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {shares.map((share) => {
            const video = getVideoForShare(share);
            return (
              <div
                key={share.id}
                className={`bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-900/20 overflow-hidden hover:shadow-lg dark:hover:shadow-gray-900/40 transition-all duration-200 ${
                  selectedShares.has(share.id) ? 'ring-2 ring-primary-500 dark:ring-primary-400' : ''
                }`}
              >
                {/* Selection Checkbox */}
                {isSelectionMode && (
                  <div className="absolute top-2 left-2 z-10">
                    <input
                      type="checkbox"
                      checked={selectedShares.has(share.id)}
                      onChange={() => handleSelectShare(share.id)}
                      className="w-5 h-5 text-primary-600 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-primary-500 dark:focus:ring-primary-400 focus:ring-2"
                    />
                  </div>
                )}

                {/* Video Thumbnail */}
                <div className="aspect-video bg-gray-200 dark:bg-gray-700 relative">
                  {video?.thumbnail_path ? (
                    <img
                      src={`/api/videos/thumbnail/${video.thumbnail_path}`}
                      alt={video.title || video.original_filename}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <ShareIcon className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                </div>

                {/* Share Info */}
                <div className="p-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 truncate">
                    {video?.title || video?.original_filename || 'Unknown Video'}
                  </h3>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Shared with:</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {share.user_username}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Permission:</span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPermissionColor(share.permission_level)}`}>
                        {getPermissionIcon(share.permission_level)}
                        <span className="ml-1 capitalize">{share.permission_level}</span>
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Shared:</span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {formatDate(share.shared_at)}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Access Count:</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {share.access_count || 0}
                      </span>
                    </div>
                    
                    {share.last_accessed_at && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Last Accessed:</span>
                        <span className="text-sm text-gray-900 dark:text-white">
                          {formatDate(share.last_accessed_at)}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="mt-4 flex items-center justify-between">
                    <button className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300">
                      Edit Permissions
                    </button>
                    <button className="text-sm text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300">
                      Revoke Access
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && shares.length === 0 && (
        <div className="text-center py-12">
          <ShareIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No shared videos</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {searchTerm ? 'No videos match your search criteria.' : 'You haven\'t shared any videos yet.'}
          </p>
        </div>
      )}

      {/* Bulk Share Modal */}
      <BulkShareModal
        isOpen={showBulkShareModal}
        onClose={() => setShowBulkShareModal(false)}
        videoIds={[]} // This would be populated with selected video IDs
        videoTitles={[]} // This would be populated with selected video titles
      />
    </div>
  );
};

export default SharedByMePage;
