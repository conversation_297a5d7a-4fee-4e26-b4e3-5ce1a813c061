import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { MagnifyingGlassIcon, FunnelIcon, ChevronDownIcon, ChevronUpIcon, Squares2X2Icon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

import { videoApi, tagApi, testMobileConnection } from '../utils/api';
import { VideoFilter, PaginationParams, Video, Tag, TagCloudData } from '../types';
import VideoGrid from '../components/VideoGrid';
import TagCloud from '../components/TagCloud';
import SearchBar from '../components/SearchBar';
import FilterPanel from '../components/FilterPanel';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import BulkActionsToolbar from '../components/BulkActionsToolbar';
import BulkTagModal from '../components/BulkTagModal';
import ConfirmationDialog from '../components/ConfirmationDialog';
import ServerOverloadMessage from '../components/ServerOverloadMessage';
import { useVideoSelection } from '../contexts/VideoSelectionContext';

const HomePage: React.FC = () => {
  console.log('🏠 HomePage: Component is rendering - THIS SHOULD NOT HAPPEN WITHOUT AUTHENTICATION!');
  const queryClient = useQueryClient();
  const {
    isSelectionMode,
    getSelectedCount,
    getSelectedVideoIds,
    enterSelectionMode,
    exitSelectionMode
  } = useVideoSelection();

  const [filters, setFilters] = useState<VideoFilter>({});
  const [pagination, setPagination] = useState<PaginationParams>({ skip: 0, limit: 50 });
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [connectionTest, setConnectionTest] = useState<any>(null);
  const [apiReachable, setApiReachable] = useState<boolean | null>(null);
  const [showSlowLoadingWarning, setShowSlowLoadingWarning] = useState(false);
  const [allVideos, setAllVideos] = useState<Video[]>([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [showTagCloud, setShowTagCloud] = useState(false);

  // Bulk operations state
  const [showBulkTagModal, setShowBulkTagModal] = useState(false);
  const [bulkTagMode, setBulkTagMode] = useState<'add' | 'remove'>('add');
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  // Ref to track the last processed video data to prevent duplicate processing
  const lastProcessedVideos = useRef<string>('');

  // Fetch videos with mobile-friendly retry and better error handling
  const {
    data: newVideos = [],
    isPending: videosLoading,
    error: videosError,
    refetch: refetchVideos
  } = useQuery<Video[]>({
    queryKey: ['videos', pagination, filters],
    queryFn: async () => {
      console.log('Fetching videos with pagination:', pagination, 'filters:', filters);
      try {
        const result = await videoApi.getVideos(pagination, filters);
        console.log('Videos fetched successfully:', result?.length || 0, 'videos');
        return result;
      } catch (error) {
        console.error('Error fetching videos:', error);
        throw error;
      }
    },
    retry: (failureCount, error: any) => {
      console.log('Query retry attempt:', failureCount, 'error:', error?.response?.status);
      // Don't retry on 504 Gateway Timeout - server is overloaded
      if (error?.response?.status === 504) {
        return false;
      }
      // Retry up to 2 times for other errors (reduced from 3)
      if (failureCount < 2) {
        return true;
      }
      return false;
    },
    retryDelay: (attemptIndex) => Math.min(2000 * 2 ** attemptIndex, 30000), // Slower exponential backoff
    staleTime: 30000, // Consider data stale after 30 seconds (increased)
    gcTime: 60000, // Keep in cache for 1 minute
    networkMode: 'online', // Only run when online
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnMount: true, // Always refetch on mount
  });

  // Test mobile connection on mount with better error handling
  useEffect(() => {
    const runConnectionTest = async () => {
      try {
        const result = await testMobileConnection();
        setConnectionTest(result);
        setApiReachable(result.success);

        // If connection fails with 504, show a specific message
        if (!result.success && result.details?.status === 504) {
          setConnectionTest({
            ...result,
            error: 'Server is temporarily overloaded. Please wait a moment and try again.'
          });
        }
      } catch (error) {
        console.error('Connection test failed:', error);
        setApiReachable(false);
        setConnectionTest({
          success: false,
          error: 'Connection test failed. The server may be temporarily unavailable.'
        });
      }
    };

    runConnectionTest();
  }, []);

  // Add a timeout for the videos query if it's taking too long
  useEffect(() => {
    if (videosLoading && apiReachable === true) {
      const timeout = setTimeout(() => {
        console.warn('Videos query is taking too long, this might indicate a server issue');
        setShowSlowLoadingWarning(true);
      }, 10000); // 10 second warning

      return () => clearTimeout(timeout);
    } else {
      setShowSlowLoadingWarning(false);
    }
  }, [videosLoading, apiReachable]);

  // Handle video accumulation for pagination
  useEffect(() => {
    // Create a unique key for the current video data to prevent duplicate processing
    // Include video IDs to detect if we're getting the same data with different pagination
    const videoIds = newVideos.map(v => v.id).sort().join(',');
    const currentVideoKey = `${newVideos.length}-${pagination.skip}-${videosLoading}-${videoIds}`;



    // Skip if we've already processed this exact data
    if (currentVideoKey === lastProcessedVideos.current) {
      return;
    }

    // Additional check: if we're loading more but getting first page data, skip
    if (pagination.skip > 0 && newVideos.length > 0 && !videosLoading) {
      const firstVideoId = newVideos[0]?.id;
      const isFirstPageData = allVideos.length > 0 && allVideos[0]?.id === firstVideoId;
      if (isFirstPageData) {
        return;
      }
    }

    if (newVideos.length > 0 && !videosLoading) {
      lastProcessedVideos.current = currentVideoKey;

      if (pagination.skip === 0) {
        // First page or filter change - replace all videos
        setAllVideos(newVideos);
      } else {
        // Loading more - append to existing videos
        setAllVideos(prev => {
          const existingIds = new Set(prev.map(v => v.id));
          const uniqueNewVideos = newVideos.filter(v => !existingIds.has(v.id));
          return [...prev, ...uniqueNewVideos];
        });
      }
      setIsLoadingMore(false);
      // Check if we have more videos to load
      setHasMore(newVideos.length === pagination.limit);
    }
  }, [newVideos, pagination.skip, videosLoading]);

  // Infinite scroll implementation
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          // Get scroll position
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          const windowHeight = window.innerHeight;
          const documentHeight = document.documentElement.scrollHeight;

          // Check if we're near the bottom (within 200px)
          const isNearBottom = scrollTop + windowHeight >= documentHeight - 200;

          // Check if videos are actually rendered (more reliable than state)
          const renderedVideos = document.querySelectorAll('[class*="aspect-video"]').length;

          if (isNearBottom && hasMore && !isLoadingMore && !videosLoading && renderedVideos > 0) {
            handleLoadMore();
          }

          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasMore, isLoadingMore, videosLoading]);

  // Fetch tags for filter panel
  const {
    data: tags = [],
    isPending: tagsLoading,
  } = useQuery<Tag[]>({
    queryKey: ['tags'],
    queryFn: () => tagApi.getTags({ skip: 0, limit: 100 }),
  });

  // Fetch tag cloud data
  const {
    data: tagCloudData = [],
  } = useQuery<TagCloudData[]>({
    queryKey: ['tagCloud'],
    queryFn: () => tagApi.getTagCloudData(),
  });

  // Bulk operations mutations
  const bulkAddTagsMutation = useMutation({
    mutationFn: ({ videoIds, tagIds }: { videoIds: number[], tagIds: number[] }) =>
      videoApi.bulkAddTags(videoIds, tagIds),
    onSuccess: (data) => {
      toast.success(`Successfully added tags to ${data.processed_count} videos`);
      // Invalidate all video queries regardless of pagination/filters
      queryClient.invalidateQueries({ queryKey: ['videos'] });
      queryClient.invalidateQueries({ queryKey: ['video'] });
      queryClient.invalidateQueries({ queryKey: ['tagCloud'] });
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      // Use predicate to catch all video-related queries
      queryClient.invalidateQueries({ predicate: (query) =>
        query.queryKey[0] === 'videos' || query.queryKey[0] === 'video'
      });
      setShowBulkTagModal(false);
    },
    onError: (error: any) => {
      toast.error('Failed to add tags: ' + (error.message || 'Unknown error'));
    },
  });

  const bulkRemoveTagsMutation = useMutation({
    mutationFn: ({ videoIds, tagIds }: { videoIds: number[], tagIds: number[] }) =>
      videoApi.bulkRemoveTags(videoIds, tagIds),
    onSuccess: (data) => {
      toast.success(`Successfully removed tags from ${data.processed_count} videos`);
      // Invalidate all video queries regardless of pagination/filters
      queryClient.invalidateQueries({ queryKey: ['videos'] });
      queryClient.invalidateQueries({ queryKey: ['video'] });
      queryClient.invalidateQueries({ queryKey: ['tagCloud'] });
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      // Use predicate to catch all video-related queries
      queryClient.invalidateQueries({ predicate: (query) =>
        query.queryKey[0] === 'videos' || query.queryKey[0] === 'video'
      });
      setShowBulkTagModal(false);
    },
    onError: (error: any) => {
      toast.error('Failed to remove tags: ' + (error.message || 'Unknown error'));
    },
  });

  const bulkDeleteMutation = useMutation({
    mutationFn: (videoIds: number[]) => videoApi.bulkDeleteVideos(videoIds),
    onSuccess: (data) => {
      toast.success(`Successfully deleted ${data.processed_count} videos`);
      // Invalidate all video queries regardless of pagination/filters
      queryClient.invalidateQueries({ queryKey: ['videos'] });
      queryClient.invalidateQueries({ queryKey: ['video'] });
      queryClient.invalidateQueries({ queryKey: ['tagCloud'] });
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      // Use predicate to catch all video-related queries
      queryClient.invalidateQueries({ predicate: (query) =>
        query.queryKey[0] === 'videos' || query.queryKey[0] === 'video'
      });
      exitSelectionMode();
      setShowDeleteConfirmation(false);
      // Reset pagination to first page
      setPagination({ skip: 0, limit: 50 });
    },
    onError: (error: any) => {
      toast.error('Failed to delete videos: ' + (error.message || 'Unknown error'));
    },
  });

  // Handle search (memoized to prevent SearchBar useEffect from triggering repeatedly)
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setFilters(prev => ({ ...prev, search: query || undefined }));
    setPagination(prev => ({ ...prev, skip: 0 })); // Reset to first page
  }, []);

  // Handle filter changes (memoized to prevent unnecessary re-renders)
  const handleFilterChange = useCallback((newFilters: Partial<VideoFilter>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, skip: 0 })); // Reset to first page
    setAllVideos([]); // Clear existing videos when filters change
    setHasMore(true); // Reset hasMore when filters change
  }, []);

  // Handle tag click from tag cloud
  const handleTagClick = (tagName: string) => {
    const currentTags = filters.tags || [];
    const newTags = currentTags.includes(tagName)
      ? currentTags.filter(t => t !== tagName)
      : [...currentTags, tagName];

    handleFilterChange({ tags: newTags.length > 0 ? newTags : undefined });
  };

  // Bulk operation handlers
  const handleBulkAddTags = () => {
    setBulkTagMode('add');
    setShowBulkTagModal(true);
  };

  const handleBulkRemoveTags = () => {
    setBulkTagMode('remove');
    setShowBulkTagModal(true);
  };

  const handleBulkDelete = () => {
    setShowDeleteConfirmation(true);
  };

  const handleConfirmBulkAddTags = (tagIds: number[]) => {
    const videoIds = getSelectedVideoIds();
    bulkAddTagsMutation.mutate({ videoIds, tagIds });
  };

  const handleConfirmBulkRemoveTags = (tagIds: number[]) => {
    const videoIds = getSelectedVideoIds();
    bulkRemoveTagsMutation.mutate({ videoIds, tagIds });
  };

  const handleConfirmBulkDelete = () => {
    const videoIds = getSelectedVideoIds();
    bulkDeleteMutation.mutate(videoIds);
  };

  const handleEnterSelectionMode = () => {
    enterSelectionMode();
  };

  // Handle pagination
  const handleLoadMore = () => {
    setIsLoadingMore(true);
    setPagination(prev => ({ ...prev, skip: prev.skip + prev.limit }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
    setPagination({ skip: 0, limit: 50 });
    setAllVideos([]); // Clear existing videos when clearing filters
    setHasMore(true); // Reset hasMore when clearing filters
  };

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return !!(filters.search || filters.tags?.length || filters.language || filters.processed !== undefined);
  }, [filters]);

  if (videosError) {
    // Check if it's a 504 Gateway Timeout error (server overloaded)
    const isServerOverloaded = (videosError as any)?.response?.status === 504;
    // Check if it's a timeout error (server hanging/unresponsive)
    const isTimeout = (videosError as any)?.code === 'ECONNABORTED' ||
                     (videosError as any)?.message?.includes('timeout');

    if (isServerOverloaded || isTimeout) {
      return (
        <ServerOverloadMessage
          onRetry={() => refetchVideos()}
          message={isTimeout
            ? "The server is not responding. This usually happens during heavy video processing. Please wait and try again."
            : "The server is temporarily overloaded due to video processing. Please wait a moment and try again."
          }
        />
      );
    }

    return (
      <div className="space-y-4">
        <ErrorMessage
          message="Failed to load videos"
          onRetry={() => refetchVideos()}
        />

        {/* Mobile debugging information */}
        {connectionTest && (
          <div className="bg-gray-50 rounded-lg p-4 text-sm">
            <h3 className="font-medium text-gray-900 mb-2">Connection Test Results:</h3>
            {connectionTest.success ? (
              <div className="text-green-600">
                ✅ Backend connection successful
                <pre className="mt-2 text-xs bg-white p-2 rounded border">
                  {JSON.stringify(connectionTest.data, null, 2)}
                </pre>
              </div>
            ) : (
              <div className="text-red-600">
                ❌ Backend connection failed
                <div className="mt-2 text-xs">
                  <div><strong>Error:</strong> {connectionTest.error}</div>
                  {connectionTest.details && (
                    <pre className="mt-2 bg-white p-2 rounded border">
                      {JSON.stringify(connectionTest.details, null, 2)}
                    </pre>
                  )}
                </div>
              </div>
            )}
            <div className="mt-2 text-xs text-gray-500">
              <div><strong>Current URL:</strong> {window.location.href}</div>
              <div><strong>Detected API URL:</strong> {connectionTest.apiUrl || 'Unknown'}</div>
              <div><strong>Hostname:</strong> {window.location.hostname}</div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Video Library</h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            Manage and organize your TikTok videos with AI-powered tagging
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          {!isSelectionMode && (
            <button
              onClick={handleEnterSelectionMode}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
            >
              <Squares2X2Icon className="h-4 w-4 mr-2" />
              Select Videos
            </button>
          )}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
            {hasActiveFilters && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                Active
              </span>
            )}
          </button>
        </div>
      </div>

      {/* Search Bar */}
      <SearchBar
        value={searchQuery}
        onChange={handleSearch}
        placeholder="Search videos by title, transcript, or filename..."
      />

      {/* Filter Panel */}
      {showFilters && (
        <FilterPanel
          filters={filters}
          tags={tags}
          onFilterChange={handleFilterChange}
          onClearFilters={clearFilters}
          hasActiveFilters={hasActiveFilters}
        />
      )}

      {/* Tag Cloud */}
      {tagCloudData.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 transition-colors duration-200">
          <div
            className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 flex items-center justify-between"
            onClick={() => setShowTagCloud(!showTagCloud)}
          >
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Popular Tags</h2>
            <div className="flex items-center space-x-2">
              {filters.tags && filters.tags.length > 0 && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                  {filters.tags.length} selected
                </span>
              )}
              {showTagCloud ? (
                <ChevronUpIcon className="h-5 w-5 text-gray-500" />
              ) : (
                <ChevronDownIcon className="h-5 w-5 text-gray-500" />
              )}
            </div>
          </div>
          {showTagCloud && (
            <div className="px-6 pb-6">
              <TagCloud
                tags={tagCloudData}
                selectedTags={filters.tags || []}
                onTagClick={handleTagClick}
              />
            </div>
          )}
        </div>
      )}

      {/* Video Grid */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 transition-colors duration-200">
        {videosLoading && pagination.skip === 0 ? (
          <div className="flex flex-col justify-center items-center h-64 space-y-4">
            <LoadingSpinner size="lg" />
            {showSlowLoadingWarning && (
              <div className="text-center max-w-md">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Loading is taking longer than usual. The server might be processing videos.
                </p>
                <button
                  onClick={() => refetchVideos()}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
                >
                  Try Again
                </button>
              </div>
            )}
          </div>
        ) : (
          <VideoGrid
            videos={allVideos}
            onLoadMore={handleLoadMore}
            hasMore={hasMore}
            loading={isLoadingMore}
          />
        )}
      </div>

      {/* Empty State */}
      {!videosLoading && allVideos.length === 0 && (
        <div className="text-center py-12">
          <div className="mx-auto h-12 w-12 text-gray-400">
            <MagnifyingGlassIcon />
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No videos found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {hasActiveFilters 
              ? 'Try adjusting your search criteria or filters.'
              : 'Get started by uploading your first video.'
            }
          </p>
          {hasActiveFilters && (
            <div className="mt-6">
              <button
                onClick={clearFilters}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Clear filters
              </button>
            </div>
          )}
        </div>
      )}

      {/* Bulk Actions Toolbar */}
      <BulkActionsToolbar
        onAddTags={handleBulkAddTags}
        onRemoveTags={handleBulkRemoveTags}
        onDelete={handleBulkDelete}
        totalVideos={allVideos.length}
      />

      {/* Bulk Tag Modal */}
      <BulkTagModal
        isOpen={showBulkTagModal}
        onClose={() => setShowBulkTagModal(false)}
        onAddTags={handleConfirmBulkAddTags}
        onRemoveTags={handleConfirmBulkRemoveTags}
        mode={bulkTagMode}
        selectedVideoCount={getSelectedCount()}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showDeleteConfirmation}
        onClose={() => setShowDeleteConfirmation(false)}
        onConfirm={handleConfirmBulkDelete}
        title="Delete Selected Videos"
        message={`Are you sure you want to delete ${getSelectedCount()} selected video${getSelectedCount() !== 1 ? 's' : ''}? This action cannot be undone and will permanently remove the video files from the server.`}
        confirmText="Delete Videos"
        type="danger"
        isLoading={bulkDeleteMutation.isPending}
      />
    </div>
  );
};

export default HomePage;
